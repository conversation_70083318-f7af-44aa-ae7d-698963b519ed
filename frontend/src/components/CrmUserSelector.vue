<template>
  <a-modal
    v-model:visible="visible"
    title="选择CRM用户"
    width="800px"
    :confirm-loading="loading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <!-- 搜索区域 -->
    <div class="search-section">
      <a-input-search
        v-model="searchKeyword"
        placeholder="请输入用户姓名进行搜索"
        search-button
        @search="handleSearch"
        @press-enter="handleSearch"
        :loading="searching"
      />
    </div>

    <!-- 用户列表 -->
    <div class="user-list" v-if="userList.length > 0">
      <div class="user-grid">
        <div
          v-for="user in userList"
          :key="user.userId"
          class="user-card"
          :class="{ selected: selectedUser?.userId === user.userId }"
          @click="selectUser(user)"
        >
          <div class="user-avatar">
            <a-avatar :size="48">
              <img
                v-if="user.userPicUrl"
                :src="user.userPicUrl"
                alt="avatar"
                @error="handleImageError"
              />
              <span v-else class="avatar-text">
                {{ user.userName?.charAt(0) }}
              </span>
            </a-avatar>
          </div>
          <div class="user-info">
            <div class="user-name">{{ user.userName }}</div>
            <div class="user-department">{{ user.departmentName }}</div>
            <div class="user-id">ID: {{ user.userId }}</div>
          </div>
          <div class="selection-indicator" v-if="selectedUser?.userId === user.userId">
            <a-icon name="check-circle" />
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else-if="!searching && searchKeyword">
      <a-empty description="未找到匹配的用户" />
    </div>

    <!-- 初始状态 -->
    <div class="initial-state" v-else-if="!searchKeyword">
      <a-empty description="请输入用户姓名进行搜索" />
    </div>

    <!-- 已选择用户信息 -->
    <div class="selected-info" v-if="selectedUser">
      <a-divider>已选择用户</a-divider>
      <div class="selected-user-card">
        <a-avatar :size="40">
          <img
            v-if="selectedUser.userPicUrl"
            :src="selectedUser.userPicUrl"
            alt="avatar"
            @error="handleImageError"
          />
          <span v-else class="avatar-text">
            {{ selectedUser.userName?.charAt(0) }}
          </span>
        </a-avatar>
        <div class="selected-user-info">
          <div class="name">{{ selectedUser.userName }}</div>
          <div class="department">{{ selectedUser.departmentName }}</div>
          <div class="id">用户ID: {{ selectedUser.userId }}</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { crmIntegrationAPI } from '@/services/api';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userName: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const searching = ref(false);
const searchKeyword = ref('');
const userList = ref([]);
const selectedUser = ref(null);

// 监听props变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    // 弹窗打开时，如果有传入的用户名，自动搜索
    if (props.userName) {
      searchKeyword.value = props.userName;
      handleSearch();
    }
  } else {
    // 弹窗关闭时重置状态
    resetState();
  }
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
});

// 重置状态
const resetState = () => {
  searchKeyword.value = '';
  userList.value = [];
  selectedUser.value = null;
  loading.value = false;
  searching.value = false;
};

// 搜索用户
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    Message.warning('请输入用户姓名');
    return;
  }

  try {
    searching.value = true;
    const response = await crmIntegrationAPI.getUserByName({
      userName: searchKeyword.value.trim(),
      page: 1,
      pageSize: 20
    });

    if (response.success) {
      userList.value = response.data || [];
      if (userList.value.length === 0) {
        Message.info('未找到匹配的用户');
      } else {
        Message.success(`找到 ${userList.value.length} 个用户`);
      }
    } else {
      Message.error(response.message || '搜索失败');
      userList.value = [];
    }
  } catch (error) {
    console.error('搜索CRM用户失败:', error);
    Message.error('搜索失败，请稍后重试');
    userList.value = [];
  } finally {
    searching.value = false;
  }
};

// 选择用户
const selectUser = (user) => {
  selectedUser.value = user;
};

// 确认选择
const handleConfirm = () => {
  if (!selectedUser.value) {
    Message.warning('请选择一个用户');
    return;
  }

  loading.value = true;
  
  // 模拟一点延迟，让用户感知到操作
  setTimeout(() => {
    emit('confirm', selectedUser.value);
    visible.value = false;
    loading.value = false;
  }, 300);
};

// 取消选择
const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

// 头像加载失败处理
const handleImageError = (event) => {
  event.target.style.display = 'none';
};
</script>

<style scoped>
.search-section {
  margin-bottom: 20px;
}

.user-list {
  max-height: 400px;
  overflow-y: auto;
}

.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.user-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.user-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.user-card.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.user-avatar {
  flex-shrink: 0;
  margin-right: 12px;
}

.avatar-text {
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
}

.user-department {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.user-id {
  font-size: 11px;
  color: #9ca3af;
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #3b82f6;
  font-size: 16px;
}

.empty-state,
.initial-state {
  padding: 40px 0;
  text-align: center;
}

.selected-info {
  margin-top: 20px;
}

.selected-user-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.selected-user-info {
  margin-left: 12px;
}

.selected-user-info .name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
}

.selected-user-info .department {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.selected-user-info .id {
  font-size: 11px;
  color: #9ca3af;
}
</style>
