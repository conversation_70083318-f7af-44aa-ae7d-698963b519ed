<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      :width="240"
      :collapsed="collapsed"
      collapsible
      hide-trigger
      @collapse="onCollapse"
      class="layout-sider"
    >
      <!-- Logo 区域 -->
      <div class="logo">
        <div class="logo-content">
          <div class="logo-icon">
            <img src="@/assets/img/logo.svg" alt="AgentX" />
          </div>
          <span v-if="!collapsed" class="logo-text">AgentX 服务中心</span>
        </div>
      </div>

      <!-- 主要功能按钮 -->
      <!-- <div class="main-actions" v-if="!collapsed">
        <a-button type="primary" class="create-btn" @click="handleCreateContent">
          <template #icon>
            <icon-plus />
          </template>
          新建内容
        </a-button>
      </div> -->

      <!-- 导航菜单 -->
      <div class="nav-menu">
        <!-- 内容管理 -->
        <div class="menu-section" v-if="!collapsed">
          <div class="section-title">内容管理</div>
          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('dashboard') }"
            @click="onMenuClick('dashboard')"
          >
            <div class="menu-icon">
              <icon-home />
            </div>
            <span v-if="!collapsed" class="menu-text">首页</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('public-influencers') }"
            @click="onMenuClick('public-influencers')"
          >
            <div class="menu-icon">
              <icon-user-group />
            </div>
            <span class="menu-text">达人公海</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('author-videos') }"
            @click="onMenuClick('author-videos')"
          >
            <div class="menu-icon">
              <icon-play-arrow />
            </div>
            <span class="menu-text">达人作品库</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('influencers') }"
            @click="onMenuClick('influencers')"
          >
            <div class="menu-icon">
              <icon-user />
            </div>
            <span class="menu-text">我的达人</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('agent') }"
            @click="onMenuClick('agent')"
          >
            <div class="menu-icon">
              <icon-message />
            </div>
            <span class="menu-text">AI智能助手</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('influencer-reports') }"
            @click="onMenuClick('influencer-reports')"
          >
            <div class="menu-icon">
              <icon-file />
            </div>
            <span class="menu-text">达人提报</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('cooperation') }"
            @click="onMenuClick('cooperation')"
          >
            <div class="menu-icon">
              <icon-link />
            </div>
            <span class="menu-text">合作对接</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('data-fetch-tasks') }"
            @click="onMenuClick('data-fetch-tasks')"
          >
            <div class="menu-icon">
              <icon-download />
            </div>
            <span class="menu-text">笔记数据拉取</span>
          </div>
        </div>

        <!-- 折叠状态下的菜单项 -->
        <div v-if="collapsed" class="collapsed-menu">
          <!-- 首页 -->
          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('dashboard') }"
            @click="onMenuClick('dashboard')"
            :title="'首页'"
          >
            <div class="menu-icon">
              <icon-home />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('influencers') }"
            @click="onMenuClick('influencers')"
            :title="'我的达人'"
          >
            <div class="menu-icon">
              <icon-user />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('public-influencers') }"
            @click="onMenuClick('public-influencers')"
            :title="'达人公海'"
          >
            <div class="menu-icon">
              <icon-user-group />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('influencer-reports') }"
            @click="onMenuClick('influencer-reports')"
            :title="'达人提报'"
          >
            <div class="menu-icon">
              <icon-file />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('cooperation') }"
            @click="onMenuClick('cooperation')"
            :title="'合作对接'"
          >
            <div class="menu-icon">
              <icon-link />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('data-fetch-tasks') }"
            @click="onMenuClick('data-fetch-tasks')"
            :title="'笔记数据拉取'"
          >
            <div class="menu-icon">
              <icon-download />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('crm-integration') }"
            @click="onMenuClick('crm-integration')"
            :title="'CRM集成'"
          >
            <div class="menu-icon">
              <icon-relation />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('author-videos') }"
            @click="onMenuClick('author-videos')"
            :title="'达人作品库'"
          >
            <div class="menu-icon">
              <icon-play-arrow />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('crawler') }"
            @click="onMenuClick('crawler')"
            :title="'爬虫管理'"
          >
            <div class="menu-icon">
              <icon-robot />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('cookies') }"
            @click="onMenuClick('cookies')"
            :title="'Cookie管理'"
          >
            <div class="menu-icon">
              <icon-settings />
            </div>
          </div>

          <div
            class="menu-item"
            :class="{ active: selectedKeys.includes('agent') }"
            @click="onMenuClick('agent')"
            :title="'AI智能助手'"
          >
            <div class="menu-icon">
              <icon-message />
            </div>
          </div>
        </div>

        <!-- 数据中心 -->
        <div class="menu-section" v-if="!collapsed">
          <div class="section-title">数据中心</div>

          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('crawler') }"
            @click="onMenuClick('crawler')"
          >
            <div class="menu-icon">
              <icon-robot />
            </div>
            <span class="menu-text">爬虫管理</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('crm-integration') }"
            @click="onMenuClick('crm-integration')"
          >
            <div class="menu-icon">
              <icon-relation />
            </div>
            <span class="menu-text">CRM集成</span>
          </div>
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('cookies') }"
            @click="onMenuClick('cookies')"
          >
            <div class="menu-icon">
              <icon-settings />
            </div>
            <span class="menu-text">Cookie管理</span>
          </div>
          <!-- 账号管理 - 仅管理员可见 -->
          <div
            v-if="userStore.user?.role === 'admin'"
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('users') }"
            @click="onMenuClick('users')"
          >
            <div class="menu-icon">
              <icon-user-group />
            </div>
            <span class="menu-text">账号管理</span>
          </div>
          <!-- 字典管理 -->
          <div
            class="menu-item sub-item"
            :class="{ active: selectedKeys.includes('dictionaries') }"
            @click="onMenuClick('dictionaries')"
          >
            <div class="menu-icon">
              <icon-file />
            </div>
            <span class="menu-text">字典管理</span>
          </div>
        </div>
      </div>
    </a-layout-sider>

    <!-- 主内容区域 -->
    <a-layout class="layout-content">
      <!-- 顶部导航栏 -->
      <a-layout-header class="layout-header">
        <div class="header-left">
          <a-button type="text" @click="toggleCollapsed" class="collapse-btn">
            <icon-menu-fold v-if="!collapsed" />
            <icon-menu-unfold v-if="collapsed" />
          </a-button>

          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item>AgentX</a-breadcrumb-item>
            <a-breadcrumb-item>{{ currentPageTitle }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 用户信息下拉菜单 -->
          <a-dropdown>
            <div class="user-info">
              <a-avatar class="user-avatar">
                <img
                  v-if="userStore.user?.crmUserPicUrl"
                  :src="userStore.user.crmUserPicUrl"
                  alt="avatar"
                  @error="handleAvatarError"
                />
                <span v-else class="avatar-text">
                  {{ (userStore.user?.chineseName || userStore.user?.username)?.charAt(0).toUpperCase() }}
                </span>
              </a-avatar>
              <span class="username">{{ userStore.user?.chineseName || userStore.user?.username }}</span>
              <icon-down />
            </div>
            <template #content>
              <a-doption @click="handleLogout">
                <icon-poweroff />
                退出登录
              </a-doption>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 页面内容 -->
      <a-layout-content class="page-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/stores/user';
import {
  IconUser,
  IconUserGroup,
  IconRobot,
  IconSettings,
  IconMenuFold,
  IconMenuUnfold,
  IconDown,
  IconPoweroff,
  // IconPlus,
  IconHome,
  IconBarChart,
  IconCalendar,
  IconPlayArrow,
  IconMessage,
  IconFile,
  IconLink,
  IconDownload,
  IconRelation
} from '@arco-design/web-vue/es/icon';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 侧边栏折叠状态
const collapsed = ref(false);

// 当前选中的菜单项
const selectedKeys = computed(() => {
  const routeName = route.name;
  return [routeName];
});

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title || '首页';
});

// 监听路由变化，更新选中状态
watch(
  () => route.name,
  newName => {
    if (newName) {
      // 这里可以根据需要设置展开的子菜单
    }
  },
  { immediate: true }
);

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};

// 侧边栏折叠回调
const onCollapse = collapsed => {
  collapsed.value = !collapsed;
};

// 菜单点击事件
const onMenuClick = key => {
  router.push({ name: key });
};

// 新建内容
// const handleCreateContent = () => {
//   // 这里可以打开新建内容的弹窗或跳转到创建页面
//   console.log('新建内容')
// }

// 退出登录
const handleLogout = () => {
  userStore.logout();
  router.push('/login');
};

// 头像加载失败处理
const handleAvatarError = (event) => {
  // 隐藏失败的图片，显示文字头像
  event.target.style.display = 'none';
};
</script>

<style scoped>
.main-layout {
  height: 100vh;
  background: url('@/assets/img/bg3.png') no-repeat center center fixed;
  background-size: cover;
}

.layout-sider {
  background: transparent;
  border-right: 1px solid #e5e7eb;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.04);
}

/* Logo 区域 */
.logo {
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 20px;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

/* 折叠状态下的logo居中显示 */
.layout-sider.arco-layout-sider-collapsed .logo {
  justify-content: center;
  padding: 0 12px;
}

.logo-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  white-space: nowrap;
}

/* 主要功能按钮 */
.main-actions {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
}

.create-btn {
  width: 100%;
  height: 40px;
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
}

.create-btn:hover {
  background: linear-gradient(135deg, #ff3742 0%, #ff2d3a 100%);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.4);
  transform: translateY(-1px);
}

/* 导航菜单 */
.nav-menu {
  padding: 8px 0;
  overflow-y: auto;
  height: calc(100vh - 200px);
}

.menu-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0 20px 8px;
  margin-bottom: 8px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  margin: 0 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.menu-item:hover {
  background: #f9fafb;
}

.menu-item.active {
  background: #eff6ff;
  color: #2563eb;
}

.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: #2563eb;
  border-radius: 0 2px 2px 0;
}

.menu-item.sub-item {
  margin-left: 20px;
  padding-left: 16px;
}

.menu-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  color: #6b7280;
}

.menu-item.active .menu-icon {
  color: #2563eb;
}

.menu-text {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.menu-item.active .menu-text {
  color: #2563eb;
  font-weight: 600;
}

/* 折叠状态菜单 */
.collapsed-menu .menu-item {
  justify-content: center;
  padding: 12px;
  margin: 4px 8px;
}

.collapsed-menu .menu-icon {
  margin-right: 0;
}

.layout-header {
  background: hsla(0, 0%, 100%, 0.2);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  font-size: 16px;
  color: #6b7280;
  border: none;
  background: transparent;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.breadcrumb {
  margin: 0;
}

.breadcrumb :deep(.arco-breadcrumb-item) {
  color: #6b7280;
  font-size: 14px;
}

.breadcrumb :deep(.arco-breadcrumb-item:last-child) {
  color: #1f2937;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.user-info:hover {
  background-color: #f9fafb;
  border-color: #e5e7eb;
}

.user-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  font-weight: 600;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 14px;
}

.username {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.page-content {
  padding: 16px;
  overflow: auto;
}

/* .content-wrapper {
  background: white;
  border-radius: 12px;
  padding: 24px;
  min-height: calc(100vh - 112px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
} */

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    position: fixed;
    z-index: 100;
    height: 100vh;
  }

  .logo-text {
    font-size: 14px;
  }

  .main-actions {
    padding: 12px 16px;
  }

  .create-btn {
    height: 36px;
    font-size: 13px;
  }

  .menu-item {
    padding: 10px 16px;
    margin: 0 8px;
  }

  .section-title {
    padding: 0 16px 6px;
  }
}

/* 滚动条样式 */
.nav-menu::-webkit-scrollbar {
  width: 4px;
}

.nav-menu::-webkit-scrollbar-track {
  background: transparent;
}

.nav-menu::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.nav-menu::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
