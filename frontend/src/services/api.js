import axios from 'axios';

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器 - 添加 token
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    if (error.response?.status === 401) {
      // token 过期，清除本地存储并跳转到登录页
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证相关 API
export const authAPI = {
  // 登录
  login: credentials => api.post('/auth/login', credentials),

  // 获取当前用户信息
  getCurrentUser: () => api.get('/auth/me'),

  // 刷新 token
  refreshToken: () => api.post('/auth/refresh')
};

// 达人管理 API
export const influencerAPI = {
  // 获取达人列表
  getList: params => api.get('/influencers', { params }),

  // 获取达人详情
  getById: id => api.get(`/influencers/${id}`),

  // 获取达人详细信息面板
  getDetailById: id => api.get(`/influencers/${id}/detail`),

  // 创建达人
  create: data => api.post('/influencers', data),

  // 更新达人
  update: (id, data) => api.put(`/influencers/${id}`, data),

  // 删除达人
  delete: id => api.delete(`/influencers/${id}`),

  // 批量删除达人
  batchDelete: ids => api.post('/influencers/batch-delete', { ids }),

  // 获取达人统计信息
  getStats: () => api.get('/influencers/stats'),

  // 导出达人信息
  export: params => api.get('/influencers/export', { params })
};

// 达人公海 API
export const publicInfluencerAPI = {
  // 获取达人公海列表
  getList: params => api.get('/public-influencers', { params }),

  // 获取达人公海详情
  getById: id => api.get(`/public-influencers/${id}`),

  // 收藏到我的达人
  importToMyInfluencers: id => api.post(`/public-influencers/${id}/import`),

  // 批量收藏到我的达人
  batchImport: ids => api.post('/public-influencers/batch-import', { ids }),

  // 导出达人公海数据到Excel
  export: params => api.get('/public-influencers/export', { params })
};

// 爬虫任务 API
export const crawlerAPI = {
  // 获取任务列表
  getList: params => api.get('/crawler/tasks', { params }),

  // 获取任务详情
  getById: id => api.get(`/crawler/tasks/${id}`),

  // 创建任务
  create: data => api.post('/crawler/tasks', data),

  // 更新任务
  update: (id, data) => api.put(`/crawler/tasks/${id}`, data),

  // 启动任务
  start: id => api.post(`/crawler/tasks/${id}/start`),

  // 停止任务（后端会将状态设置为paused）
  stop: id => api.post(`/crawler/tasks/${id}/stop`),

  // 重试任务
  retry: id => api.post(`/crawler/tasks/${id}/retry`),

  // 删除任务
  delete: id => api.delete(`/crawler/tasks/${id}`),

  // 批量删除任务
  batchDelete: ids => api.post('/crawler/tasks/batch-delete', { ids }),

  // 批量重试任务
  batchRetry: ids => api.post('/crawler/tasks/batch-retry', { ids }),

  // 获取任务结果
  getResults: (taskId, params) => api.get(`/crawler/tasks/${taskId}/results`, { params }),

  // 获取详情
  getResultDetail: resultId => api.get(`/crawler/results/${resultId}`),

  // 获取任务日志
  getLogs: (taskId, params) => api.get(`/crawler/tasks/${taskId}/logs`, { params }),

  // 导入结果到达人表
  importResults: (taskId, resultIds) => api.post(`/crawler/results/batch-import`, { ids: resultIds }),

  // 导入任务所有结果
  importTaskAllResults: taskId => api.post(`/crawler/tasks/${taskId}/import`),

  // 获取服务状态
  getServiceStatus: () => api.get('/crawler/status'),

  // 获取统计信息
  getStats: () => api.get('/crawler/stats')
};

// Cookie 管理 API
export const cookieAPI = {
  // 获取 Cookie 列表
  getList: params => api.get('/cookies', { params }),

  // 获取 Cookie 详情
  getById: id => api.get(`/cookies/${id}`),

  // 创建 Cookie
  create: data => api.post('/cookies', data),

  // 更新 Cookie
  update: (id, data) => api.put(`/cookies/${id}`, data),

  // 删除 Cookie
  delete: id => api.delete(`/cookies/${id}`),

  // 验证 Cookie
  validate: id => api.post(`/cookies/${id}/validate`)
};

// 达人作品库 API
export const authorVideoAPI = {
  // 获取作品列表
  getList: params => api.get('/author-videos', { params }),

  // 获取作品详情
  getById: id => api.get(`/author-videos/${id}`),

  // 获取作品库统计信息
  getStats: () => api.get('/author-videos/stats'),

  // 获取小红书帖子详情
  getXiaohongshuNoteDetail: noteId => api.get(`/author-videos/xiaohongshu/note/${noteId}`)
};

// 用户管理 API（仅管理员）
export const userAPI = {
  // 获取用户列表
  getUsers: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/users${queryString ? `?${queryString}` : ''}`);
  },

  // 创建新用户
  createUser: userData => api.post('/users', userData),

  // 获取用户详情
  getUser: id => api.get(`/users/${id}`),

  // 更新用户信息
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),

  // 删除用户
  deleteUser: id => api.delete(`/users/${id}`),

  // 重置用户密码
  resetPassword: (id, newPassword) => api.put(`/users/${id}/reset-password`, { newPassword }),

  // 绑定CRM用户
  bindCrmUser: (id, crmUserData) => api.put(`/users/${id}/bind-crm`, crmUserData),

  // 解绑CRM用户
  unbindCrmUser: id => api.put(`/users/${id}/unbind-crm`)
};

// 达人提报 API
export const influencerReportAPI = {
  // 获取提报列表
  getList: params => api.get('/influencer-reports', { params }),

  // 获取提报详情
  getById: id => api.get(`/influencer-reports/${id}`),

  // 创建提报
  create: data => api.post('/influencer-reports', data),

  // 更新提报信息
  update: (id, data) => api.put(`/influencer-reports/${id}`, data),

  // 更新提报状态（管理员）
  updateStatus: (id, status) => api.put(`/influencer-reports/${id}/status`, { status }),

  // 删除提报
  delete: id => api.delete(`/influencer-reports/${id}`),

  // 检查30天保护期
  checkProtection: data => api.post('/influencer-reports/check-protection', data),

  // 检查重复提报控制
  checkDuplicateReport: data => api.post('/influencer-reports/check-duplicate', data),

  // 审核通过
  approve: (id, reviewComment) => api.put(`/influencer-reports/${id}/approve`, { reviewComment }),

  // 审核拒绝
  reject: (id, reviewComment) => api.put(`/influencer-reports/${id}/reject`, { reviewComment }),

  // 需二次确认
  needConfirmation: (id, reviewComment) => api.put(`/influencer-reports/${id}/need-confirmation`, { reviewComment }),

  // 重新提报
  resubmit: (id, resubmitReason) => api.put(`/influencer-reports/${id}/resubmit`, { resubmitReason }),

  // 创建合作对接记录
  createCooperationRecord: id => api.post(`/influencer-reports/${id}/create-cooperation`),

  // 检查是否可以创建合作对接记录
  canCreateCooperationRecord: id => api.get(`/influencer-reports/${id}/can-create-cooperation`),

  // 智能提报相关接口
  // 检查Cookie状态
  checkCookieStatus: data => api.post('/influencer-reports/smart/check-cookie', data),

  // 获取达人基础信息
  getAuthorInfo: data => api.post('/influencer-reports/smart/author-info', data),

  // 获取达人所有作品
  getAuthorVideos: data => api.post('/influencer-reports/smart/author-videos', data)
};

// 字典管理API
export const dictionaryAPI = {
  // 获取所有字典分类
  getCategories: () => api.get('/dictionaries/categories'),

  // 按分类获取字典项
  getByCategory: (category, activeOnly = true) =>
    api.get(`/dictionaries/category/${category}?activeOnly=${activeOnly}`),

  // 获取字典列表（分页）
  getList: params => api.get('/dictionaries', { params }),

  // 创建字典项
  create: data => api.post('/dictionaries', data),
  createDictionary: data => api.post('/dictionaries', data),

  // 更新字典项
  update: (id, data) => api.put(`/dictionaries/${id}`, data),
  updateDictionary: (id, data) => api.put(`/dictionaries/${id}`, data),

  // 删除字典项
  delete: id => api.delete(`/dictionaries/${id}`),
  deleteDictionary: id => api.delete(`/dictionaries/${id}`),

  // 批量创建字典项
  batchCreate: dictionaries => api.post('/dictionaries/batch', { dictionaries }),
  batchCreateDictionaries: data => api.post('/dictionaries/batch', data),

  // 获取字典列表（分页）
  getDictionaryList: params => api.get('/dictionaries', { params }),

  // 批量获取字典数据
  getBatchDictionaries: (categories, activeOnly = true) => {
    const categoriesStr = Array.isArray(categories) ? categories.join(',') : categories;
    return api.get('/dictionaries/batch', {
      params: {
        categories: categoriesStr,
        activeOnly: activeOnly.toString()
      }
    });
  }
};

// 合作对接管理 API
export const cooperationAPI = {
  // 获取合作记录列表
  getList: params => api.get('/cooperation', { params }),

  // 获取合作记录详情
  getById: id => api.get(`/cooperation/${id}`),

  // 创建合作记录
  create: data => api.post('/cooperation', data),

  // 更新合作记录
  update: (id, data) => api.put(`/cooperation/${id}`, data),

  // 删除合作记录
  delete: id => api.delete(`/cooperation/${id}`),

  // 解析笔记链接
  parseNoteLink: noteLink => api.post('/cooperation/parse-note-link', { noteLink }),

  // 手动拉取笔记数据
  fetchNoteData: id => api.post(`/cooperation/${id}/fetch-data`),

  // 任务管理接口
  getDataFetchTasks: params => api.get('/cooperation/tasks', { params }),
  batchFetchNoteData: ids => api.post('/cooperation/tasks/batch-fetch', { ids }),
  getTaskStats: () => api.get('/cooperation/tasks/stats'),

  // CRM集成接口
  recreateCrmCustomer: id => api.post(`/cooperation/${id}/crm/recreate-customer`),
  recreateCrmAgreement: id => api.post(`/cooperation/${id}/crm/recreate-agreement`),

  // 获取定时任务状态
  getScheduleStatus: () => api.get('/cooperation/schedule/status'),

  // 手动触发笔记数据拉取
  triggerDataFetch: () => api.post('/cooperation/schedule/trigger'),

  // 检查Cookie状态
  checkCookieStatus: () => api.get('/cooperation/cookie/status'),

  // 获取Cookie使用统计
  getCookieStats: () => api.get('/cooperation/cookie/stats')
};

// CRM集成管理 API
export const crmIntegrationAPI = {
  // 基础功能
  testConnection: () => api.get('/crm-integration/test-connection'),
  getSystemStatus: () => api.get('/crm-integration/status'),
  getConfig: () => api.get('/crm-integration/config'),
  refreshToken: () => api.post('/crm-integration/refresh-token'),

  // 数据查询
  getCustomerList: params => api.get('/crm-integration/customers', { params }),
  getAgreementsByCustomer: customerName =>
    api.get(`/crm-integration/customers/${encodeURIComponent(customerName)}/agreements`),

  // 用户查询
  getUserByName: params => api.get('/crm-integration/users/search', { params }),

  // 数据同步
  syncCustomerData: data => api.post('/crm-integration/sync/customers', data),
  syncCompleteData: data => api.post('/crm-integration/sync/complete', data),
  syncAllData: data => api.post('/crm-integration/sync/all', data),

  // 新增智能同步功能
  smartSyncCooperationData: data => api.post('/crm-integration/smart-sync', data),
  createTestData: () => api.post('/crm-integration/test-data'),
  createCrmData: data => api.post('/crm-integration/data/create', data),
  updateCrmData: data => api.post('/crm-integration/data/update', data),
  testFieldMapping: data => api.post('/crm-integration/test-mapping', data)
};

// CRM字典管理 API
export const crmDictionaryAPI = {
  // 基础功能
  testConnection: () => api.get('/crm-dictionaries/test'),
  getSystemStatus: () => api.get('/crm-dictionaries/status'),
  refreshToken: () => api.post('/crm-dictionaries/refresh-token'),

  // 获取统计信息
  getStats: () => api.get('/crm-dictionaries/stats'),

  // 获取字典数据列表
  getDictionaries: params => api.get('/crm-dictionaries', { params }),

  // 获取字典选项（用于表单下拉）
  getDictionaryOptions: (deployId, fieldName) => api.get(`/crm-dictionaries/options/${deployId}/${fieldName}`),

  // 同步所有字典数据
  syncAll: () => api.post('/crm-dictionaries/sync'),

  // 同步指定部署类型的字典数据
  syncByDeployId: deployId => api.post(`/crm-dictionaries/sync/${deployId}`),

  // 清理过期数据
  cleanup: data => api.delete('/crm-dictionaries/cleanup', { data })
};

// 统计数据 API
export const statsAPI = {
  // 获取综合仪表板数据
  getDashboard: () => api.get('/dashboard'),

  // 获取总体统计数据
  getOverview: () => api.get('/dashboard/overview'),

  // 获取平台分布统计
  getPlatformStats: () => api.get('/dashboard/platform-stats'),

  // 获取最近活动记录
  getRecentActivities: (limit = 10) => api.get(`/dashboard/recent-activities?limit=${limit}`),

  // 获取达人采集数量趋势
  getInfluencerTrends: (days = 7) => api.get(`/dashboard/influencer-trends?days=${days}`),

  // 获取任务执行趋势
  getTaskTrends: (days = 7) => api.get(`/dashboard/task-trends?days=${days}`),

  // 获取达人统计
  getInfluencerStats: () => api.get('/influencers/stats'),

  // 获取爬虫统计
  getCrawlerStats: () => api.get('/crawler/stats')
};

export default api;
