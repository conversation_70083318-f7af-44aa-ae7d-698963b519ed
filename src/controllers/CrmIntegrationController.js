/**
 * CRM集成控制器
 *
 * 功能说明：
 * - 处理CRM数据同步相关的HTTP请求
 * - 提供CRM连接测试、数据拉取、同步等接口
 * - 实现错误处理和响应格式化
 *
 * 主要接口：
 * - testConnection: 测试CRM连接
 * - getCustomerList: 获取客户列表
 * - getAgreementsByCustomer: 获取客户协议
 * - syncCustomerData: 同步客户数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const CrmIntegrationService = require('../services/CrmIntegrationService');
const ResponseUtil = require('../utils/response');

class CrmIntegrationController {
  /**
   * 根据姓名查询CRM用户
   */
  async getUserByName(ctx) {
    try {
      const { userName, page = 1, pageSize = 20 } = ctx.query;

      if (!userName) {
        return ResponseUtil.error(ctx, '用户姓名不能为空', 400);
      }

      console.log(`🔍 查询CRM用户 - 姓名: ${userName}`);

      const crmService = new CrmIntegrationService();
      const result = await crmService.getUserByName(userName, parseInt(page), parseInt(pageSize));

      if (result.success) {
        ResponseUtil.success(ctx, result.data, `查询到 ${result.data.length} 个用户`);
      } else {
        ResponseUtil.error(ctx, result.message, 500);
      }
    } catch (error) {
      console.error('❌ CRM用户查询失败:', error.message);
      ResponseUtil.error(ctx, `CRM用户查询失败: ${error.message}`, 500);
    }
  }

  /**
   * 测试CRM连接
   */
  async testConnection(ctx) {
    try {
      console.log('🔍 开始测试CRM连接...');

      const crmService = new CrmIntegrationService();
      const result = await crmService.testConnection();

      if (result.success) {
        ResponseUtil.success(ctx, result.data, result.message);
      } else {
        ResponseUtil.error(ctx, result.message, 500);
      }
    } catch (error) {
      console.error('❌ CRM连接测试失败:', error.message);
      ResponseUtil.error(ctx, `CRM连接测试失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取客户列表
   */
  async getCustomerList(ctx) {
    try {
      const { page = 1, limit = 20 } = ctx.query;

      console.log(`📋 获取CRM客户列表 - 页码: ${page}, 每页: ${limit}`);

      const crmService = new CrmIntegrationService();
      const result = await crmService.getCustomerList(parseInt(page), parseInt(limit));

      ResponseUtil.successWithPagination(
        ctx,
        result.data,
        {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: Math.ceil(result.total / result.limit)
        },
        '获取客户列表成功'
      );
    } catch (error) {
      console.error('❌ 获取客户列表失败:', error.message);
      ResponseUtil.error(ctx, `获取客户列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 根据客户名称获取协议列表
   */
  async getAgreementsByCustomer(ctx) {
    try {
      const { customerName } = ctx.params;

      if (!customerName) {
        return ResponseUtil.error(ctx, '客户名称不能为空', 400);
      }

      console.log(`📄 获取客户协议 - 客户: ${customerName}`);

      const crmService = new CrmIntegrationService();
      const agreements = await crmService.getAgreementsByCustomer(customerName);

      ResponseUtil.success(ctx, agreements, '获取协议列表成功');
    } catch (error) {
      console.error('❌ 获取协议列表失败:', error.message);
      ResponseUtil.error(ctx, `获取协议列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取CRM系统状态
   */
  async getSystemStatus(ctx) {
    try {
      console.log('📊 获取CRM系统状态...');

      const crmService = new CrmIntegrationService();

      // 检查token状态
      const tokenValid = crmService.isTokenValid();

      // 获取基本统计信息
      let customerCount = 0;
      try {
        const customerData = await crmService.getCustomerList(1, 1);
        customerCount = customerData.total;
      } catch (error) {
        console.warn('获取客户统计失败:', error.message);
      }

      const status = {
        tokenValid,
        customerCount,
        lastTokenRefresh: crmService.tokenCache.expiresAt
          ? new Date(crmService.tokenCache.expiresAt - crmService.config.tokenCache.ttl * 1000).toISOString()
          : null,
        nextTokenRefresh: crmService.tokenCache.expiresAt
          ? new Date(crmService.tokenCache.expiresAt).toISOString()
          : null,
        queueLength: crmService.requestQueue.length,
        isProcessingQueue: crmService.isProcessingQueue
      };

      ResponseUtil.success(ctx, status, '获取系统状态成功');
    } catch (error) {
      console.error('❌ 获取系统状态失败:', error.message);
      ResponseUtil.error(ctx, `获取系统状态失败: ${error.message}`, 500);
    }
  }

  /**
   * 手动刷新token
   */
  async refreshToken(ctx) {
    try {
      console.log('🔄 手动刷新CRM token...');

      const crmService = new CrmIntegrationService();

      // 清除缓存的token
      crmService.tokenCache.corpAccessToken = null;
      crmService.tokenCache.expiresAt = null;

      // 获取新token
      const token = await crmService.getCorpAccessToken();

      ResponseUtil.success(
        ctx,
        {
          tokenValid: !!token,
          expiresAt: new Date(crmService.tokenCache.expiresAt).toISOString()
        },
        'Token刷新成功'
      );
    } catch (error) {
      console.error('❌ Token刷新失败:', error.message);
      ResponseUtil.error(ctx, `Token刷新失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取CRM配置信息（脱敏）
   */
  async getConfig(ctx) {
    try {
      const crmService = new CrmIntegrationService();

      const config = {
        proxyUrl: crmService.config.proxyUrl,
        corpId: crmService.config.corpId,
        appId: crmService.config.appId,
        // 脱敏处理
        appSecret: crmService.config.appSecret ? crmService.config.appSecret.substring(0, 8) + '****' : null,
        timeout: crmService.config.timeout,
        retryAttempts: crmService.config.retryAttempts,
        retryDelay: crmService.config.retryDelay,
        rateLimit: crmService.config.rateLimit,
        tokenCache: {
          ttl: crmService.config.tokenCache.ttl,
          refreshBuffer: crmService.config.tokenCache.refreshBuffer
        }
      };

      ResponseUtil.success(ctx, config, '获取配置信息成功');
    } catch (error) {
      console.error('❌ 获取配置信息失败:', error.message);
      ResponseUtil.error(ctx, `获取配置信息失败: ${error.message}`, 500);
    }
  }

  /**
   * 同步客户数据
   */
  async syncCustomerData(ctx) {
    try {
      const { page = 1, limit = 20, customerName } = ctx.request.body;

      console.log(
        `🔄 开始同步客户数据 - 页码: ${page}, 每页: ${limit}${customerName ? `, 客户: ${customerName}` : ''}`
      );

      const crmService = new CrmIntegrationService();

      // 获取客户列表
      const customerData = await crmService.getCustomerList(parseInt(page), parseInt(limit), customerName);

      if (customerData.data.length === 0) {
        return ResponseUtil.success(
          ctx,
          {
            message: '没有找到客户数据',
            result: { success: 0, failed: 0, skipped: 0, errors: [] }
          },
          '客户数据同步完成'
        );
      }

      // 同步客户数据
      const syncResult = await crmService.syncCustomerData(customerData.data);

      ResponseUtil.success(
        ctx,
        {
          message: `同步完成 - 成功: ${syncResult.success}, 失败: ${syncResult.failed}`,
          result: syncResult,
          totalFound: customerData.total
        },
        '客户数据同步完成'
      );
    } catch (error) {
      console.error('❌ 同步客户数据失败:', error.message);
      ResponseUtil.error(ctx, `同步客户数据失败: ${error.message}`, 500);
    }
  }

  /**
   * 完整数据同步（客户+协议）
   */
  async syncCompleteData(ctx) {
    try {
      const { page = 1, limit = 5, customerName } = ctx.request.body; // 默认每页5个，避免超时

      console.log(
        `🔄 开始完整数据同步 - 页码: ${page}, 每页: ${limit}${customerName ? `, 客户: ${customerName}` : ''}`
      );

      const crmService = new CrmIntegrationService();
      const syncResult = await crmService.syncCustomerWithAgreements(customerName, parseInt(page), parseInt(limit));

      ResponseUtil.success(
        ctx,
        {
          message: `同步完成 - 客户: ${syncResult.customerSync.success}/${syncResult.totalProcessed}, 协议: ${syncResult.agreementSync.success}`,
          result: syncResult
        },
        '完整数据同步完成'
      );
    } catch (error) {
      console.error('❌ 完整数据同步失败:', error.message);
      ResponseUtil.error(ctx, `完整数据同步失败: ${error.message}`, 500);
    }
  }

  /**
   * 全量数据同步
   */
  async syncAllData(ctx) {
    try {
      const { maxPages = 10, pageSize = 10, startPage = 1, customerName, dryRun = false } = ctx.request.body;

      console.log(
        `🔄 开始全量数据同步 - 起始页: ${startPage}, 最大页数: ${maxPages}, 每页: ${pageSize}${
          customerName ? `, 客户: ${customerName}` : ''
        }${dryRun ? ' (预览模式)' : ''}`
      );

      const crmService = new CrmIntegrationService();
      const totalResult = {
        customerSync: { success: 0, failed: 0, errors: [] },
        agreementSync: { success: 0, failed: 0, errors: [] },
        totalProcessed: 0,
        pagesProcessed: 0,
        estimatedTotal: 0,
        processingTime: 0
      };

      const startTime = Date.now();
      let currentPage = startPage;
      let hasMoreData = true;

      // 分页处理数据
      while (hasMoreData && currentPage <= startPage + maxPages - 1) {
        try {
          console.log(`📄 处理第 ${currentPage} 页数据...`);

          if (dryRun) {
            // 预览模式：只获取数据不同步
            const customerData = await crmService.getCustomerList(currentPage, pageSize, customerName);
            totalResult.totalProcessed += customerData.data.length;
            totalResult.estimatedTotal = customerData.total;

            if (customerData.data.length === 0) {
              hasMoreData = false;
            }
          } else {
            // 实际同步模式
            const syncResult = await crmService.syncCustomerWithAgreements(customerName, currentPage, pageSize);

            // 累计结果
            totalResult.customerSync.success += syncResult.customerSync.success;
            totalResult.customerSync.failed += syncResult.customerSync.failed;
            totalResult.customerSync.errors.push(...syncResult.customerSync.errors);

            totalResult.agreementSync.success += syncResult.agreementSync.success;
            totalResult.agreementSync.failed += syncResult.agreementSync.failed;
            totalResult.agreementSync.errors.push(...syncResult.agreementSync.errors);

            totalResult.totalProcessed += syncResult.totalProcessed;

            if (syncResult.totalProcessed === 0) {
              hasMoreData = false;
            }
          }

          totalResult.pagesProcessed++;
          currentPage++;

          // 添加页面间延迟，避免频率限制
          if (hasMoreData && currentPage <= startPage + maxPages - 1) {
            console.log('⏳ 等待2秒后处理下一页...');
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        } catch (error) {
          console.error(`❌ 处理第 ${currentPage} 页失败:`, error.message);
          totalResult.customerSync.errors.push({
            page: currentPage,
            error: error.message
          });
          currentPage++;
        }
      }

      totalResult.processingTime = Date.now() - startTime;

      const message = dryRun
        ? `预览完成 - 预计处理 ${totalResult.totalProcessed} 条记录，共 ${totalResult.pagesProcessed} 页`
        : `全量同步完成 - 客户: ${totalResult.customerSync.success}/${totalResult.totalProcessed}, 协议: ${
            totalResult.agreementSync.success
          }, 耗时: ${Math.round(totalResult.processingTime / 1000)}秒`;

      ResponseUtil.success(
        ctx,
        {
          message,
          result: totalResult,
          summary: {
            mode: dryRun ? 'preview' : 'sync',
            pagesProcessed: totalResult.pagesProcessed,
            totalProcessed: totalResult.totalProcessed,
            processingTimeSeconds: Math.round(totalResult.processingTime / 1000)
          }
        },
        dryRun ? '全量数据预览完成' : '全量数据同步完成'
      );
    } catch (error) {
      console.error('❌ 全量数据同步失败:', error.message);
      ResponseUtil.error(ctx, `全量数据同步失败: ${error.message}`, 500);
    }
  }

  /**
   * 智能同步合作对接数据到CRM
   */
  async smartSyncCooperationData(ctx) {
    try {
      const { cooperationId, options = {} } = ctx.request.body;

      if (!cooperationId) {
        return ResponseUtil.error(ctx, '合作对接ID不能为空', 400);
      }

      console.log(`🔄 开始智能同步合作对接数据 - ID: ${cooperationId}`);

      // 获取合作对接数据
      const { CooperationManagement } = require('../models');
      const cooperationData = await CooperationManagement.findByPk(cooperationId);

      if (!cooperationData) {
        return ResponseUtil.error(ctx, '合作对接记录不存在', 404);
      }

      const crmService = new CrmIntegrationService();
      const result = await crmService.smartSyncCooperationData(cooperationData, options);

      ResponseUtil.success(ctx, result, '智能同步完成');
    } catch (error) {
      console.error('❌ 智能同步失败:', error.message);
      ResponseUtil.error(ctx, `智能同步失败: ${error.message}`, 500);
    }
  }

  /**
   * 创建测试数据
   */
  async createTestData(ctx) {
    try {
      console.log('🧪 开始创建CRM测试数据...');

      const crmService = new CrmIntegrationService();
      const result = {
        customerId: null,
        agreementId: null,
        errors: []
      };

      try {
        // 1. 创建测试客户
        const testCustomerData = crmService.dataMappingService.createTestCustomerData();
        const customerResult = await crmService.createCrmData('customer', testCustomerData);
        result.customerId = customerResult.dataId;
        console.log(`✅ 测试客户创建成功 - ID: ${customerResult.dataId}`);

        // 2. 创建测试协议
        const testAgreementData = crmService.dataMappingService.createTestAgreementData(customerResult.dataId);
        const agreementResult = await crmService.createCrmData('contract', testAgreementData);
        result.agreementId = agreementResult.dataId;
        console.log(`✅ 测试协议创建成功 - ID: ${agreementResult.dataId}`);
      } catch (error) {
        result.errors.push(error.message);
        console.error('❌ 测试数据创建失败:', error.message);
      }

      ResponseUtil.success(ctx, result, '测试数据创建完成');
    } catch (error) {
      console.error('❌ 创建测试数据失败:', error.message);
      ResponseUtil.error(ctx, `创建测试数据失败: ${error.message}`, 500);
    }
  }

  /**
   * 更新CRM数据
   */
  async updateCrmData(ctx) {
    try {
      const { deployId, dataId, data } = ctx.request.body;

      if (!deployId || !dataId || !data) {
        return ResponseUtil.error(ctx, '参数不完整：需要deployId、dataId和data', 400);
      }

      console.log(`🔄 更新CRM数据 - 类型: ${deployId}, ID: ${dataId}`);

      const crmService = new CrmIntegrationService();
      const result = await crmService.updateCrmData(deployId, dataId, data);

      ResponseUtil.success(ctx, result, 'CRM数据更新成功');
    } catch (error) {
      console.error('❌ CRM数据更新失败:', error.message);
      ResponseUtil.error(ctx, `CRM数据更新失败: ${error.message}`, 500);
    }
  }

  /**
   * 创建CRM数据
   */
  async createCrmData(ctx) {
    try {
      const { deployId, data } = ctx.request.body;

      if (!deployId || !data) {
        return ResponseUtil.error(ctx, '参数不完整：需要deployId和data', 400);
      }

      console.log(`🔄 创建CRM数据 - 类型: ${deployId}`);

      const crmService = new CrmIntegrationService();
      const result = await crmService.createCrmData(deployId, data);

      ResponseUtil.success(ctx, result, 'CRM数据创建成功');
    } catch (error) {
      console.error('❌ CRM数据创建失败:', error.message);
      ResponseUtil.error(ctx, `CRM数据创建失败: ${error.message}`, 500);
    }
  }

  /**
   * 测试字段映射
   */
  async testFieldMapping(ctx) {
    try {
      const { cooperationData, mappingType } = ctx.request.body;

      if (!cooperationData || !mappingType) {
        return ResponseUtil.error(ctx, '参数不完整：需要cooperationData和mappingType', 400);
      }

      console.log(`🧪 测试字段映射 - 类型: ${mappingType}`);

      const crmService = new CrmIntegrationService();
      let mappedData;

      if (mappingType === 'customer') {
        mappedData = crmService.dataMappingService.mapCooperationToCustomer(cooperationData);
      } else if (mappingType === 'agreement') {
        mappedData = crmService.dataMappingService.mapCooperationToAgreement(cooperationData);
      } else {
        return ResponseUtil.error(ctx, '映射类型必须是customer或agreement', 400);
      }

      ResponseUtil.success(
        ctx,
        {
          originalData: cooperationData,
          mappedData: mappedData,
          mappingType: mappingType
        },
        '字段映射测试完成'
      );
    } catch (error) {
      console.error('❌ 字段映射测试失败:', error.message);
      ResponseUtil.error(ctx, `字段映射测试失败: ${error.message}`, 500);
    }
  }
}

module.exports = new CrmIntegrationController();
