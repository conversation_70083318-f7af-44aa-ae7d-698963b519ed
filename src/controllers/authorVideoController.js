const { AuthorVideo, MyInfluencer, sequelize } = require('../models');
const { Op } = require('sequelize');
const ResponseUtil = require('../utils/response');
const ValidationUtil = require('../utils/validation');
const CookieManager = require('../services/CookieManager');
const axios = require('axios');

/**
 * 达人作品库控制器
 * 管理达人视频作品的查询、展示等功能
 */
class AuthorVideoController {
  /**
   * @swagger
   * /api/author-videos:
   *   get:
   *     summary: 获取达人作品列表
   *     tags: [达人作品库]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *         description: 每页数量
   *       - in: query
   *         name: keyword
   *         schema:
   *           type: string
   *         description: 关键词搜索（标题、描述）
   *       - in: query
   *         name: platformUserId
   *         schema:
   *           type: string
   *         description: 星图ID筛选
   *       - in: query
   *         name: videoId
   *         schema:
   *           type: string
   *         description: 视频ID精确查找
   *       - in: query
   *         name: platform
   *         schema:
   *           type: string
   *           enum: [xiaohongshu, juxingtu, douyin]
   *         description: 平台筛选
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           enum: [publishTime, playCount, likeCount, createdAt, updatedAt]
   *           default: updatedAt
   *         description: 排序字段
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [ASC, DESC]
   *           default: DESC
   *         description: 排序方向
   *     responses:
   *       200:
   *         description: 获取作品列表成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 获取作品列表成功
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/AuthorVideo'
   *                 pagination:
   *                   $ref: '#/components/schemas/Pagination'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getAuthorVideos(ctx) {
    try {
      const {
        page = 1,
        limit = 20,
        keyword,
        authorNickname,
        platformUserId,
        videoId,
        platform,
        sortBy = 'updatedAt',
        sortOrder = 'DESC'
      } = ctx.query;

      // 参数验证
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);

      if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
        return ResponseUtil.error(ctx, '分页参数无效', 400);
      }

      // 构建查询条件
      const whereCondition = {
        status: 'active' // 只查询活跃状态的视频
      };

      // 关键词搜索（标题、描述、达人昵称）
      if (keyword && keyword.trim()) {
        whereCondition[Op.or] = [
          { title: { [Op.like]: `%${keyword.trim()}%` } },
          { description: { [Op.like]: `%${keyword.trim()}%` } },
          { authorNickname: { [Op.like]: `%${keyword.trim()}%` } }
        ];
      }

      // 达人昵称筛选
      if (authorNickname && authorNickname.trim()) {
        whereCondition.authorNickname = { [Op.like]: `%${authorNickname.trim()}%` };
      }

      // 星图ID筛选
      if (platformUserId && platformUserId.trim()) {
        whereCondition.platformUserId = platformUserId.trim();
      }

      // 视频ID精确查找
      if (videoId && videoId.trim()) {
        whereCondition.videoId = videoId.trim();
      }

      // 平台筛选
      if (platform && ['xiaohongshu', 'juxingtu', 'douyin'].includes(platform)) {
        whereCondition.platform = platform;
      }

      // 验证排序字段
      const allowedSortFields = ['publishTime', 'playCount', 'likeCount', 'createdAt', 'updatedAt'];
      const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'updatedAt';
      const sortDirection = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

      const offset = (pageNum - 1) * limitNum;

      // 查询数据
      const { count, rows } = await AuthorVideo.findAndCountAll({
        where: whereCondition,
        attributes: AuthorVideo.QUERY_ATTRIBUTES, // 排除敏感字段
        include: [{
          model: MyInfluencer,
          as: 'author',
          attributes: ['id', 'nickname', 'platform', 'platformId', 'avatarUrl'],
          required: false // 左连接，允许没有关联达人的视频
        }],
        order: [[sortField, sortDirection]],
        limit: limitNum,
        offset: offset,
        distinct: true // 确保count正确
      });

      const pagination = {
        page: pageNum,
        limit: limitNum,
        total: count,
        pages: Math.ceil(count / limitNum)
      };

      ResponseUtil.successWithPagination(ctx, rows, pagination, '获取作品列表成功');
    } catch (error) {
      console.error('获取作品列表失败:', error);
      ResponseUtil.error(ctx, '获取作品列表失败', 500);
    }
  }

  /**
   * @swagger
   * /api/author-videos/{id}:
   *   get:
   *     summary: 获取作品详情
   *     tags: [达人作品库]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 作品ID
   *     responses:
   *       200:
   *         description: 获取作品详情成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 获取作品详情成功
   *                 data:
   *                   $ref: '#/components/schemas/AuthorVideo'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getAuthorVideoById(ctx) {
    try {
      const { id } = ctx.params;

      const video = await AuthorVideo.findByPk(id, {
        attributes: AuthorVideo.QUERY_ATTRIBUTES,
        include: [{
          model: MyInfluencer,
          as: 'author',
          attributes: ['id', 'nickname', 'platform', 'platformId', 'avatarUrl', 'followersCount'],
          required: false
        }]
      });

      if (!video) {
        return ResponseUtil.notFound(ctx, '作品不存在');
      }

      ResponseUtil.success(ctx, video, '获取作品详情成功');
    } catch (error) {
      console.error('获取作品详情失败:', error);
      ResponseUtil.error(ctx, '获取作品详情失败', 500);
    }
  }

  /**
   * @swagger
   * /api/author-videos/stats:
   *   get:
   *     summary: 获取作品库统计信息
   *     tags: [达人作品库]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取统计信息成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 获取统计信息成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     totalVideos:
   *                       type: integer
   *                       description: 总视频数
   *                     totalPlays:
   *                       type: integer
   *                       description: 总播放量
   *                     totalLikes:
   *                       type: integer
   *                       description: 总点赞数
   *                     platformStats:
   *                       type: object
   *                       description: 平台分布统计
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getAuthorVideoStats(ctx) {
    try {
      // 获取总体统计
      const totalStats = await AuthorVideo.findOne({
        where: { status: 'active' },
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'totalVideos'],
          [sequelize.fn('SUM', sequelize.col('play_count')), 'totalPlays'],
          [sequelize.fn('SUM', sequelize.col('like_count')), 'totalLikes'],
          [sequelize.fn('SUM', sequelize.col('comment_count')), 'totalComments'],
          [sequelize.fn('SUM', sequelize.col('share_count')), 'totalShares']
        ],
        raw: true
      });

      // 获取平台分布统计
      const platformStats = await AuthorVideo.findAll({
        where: { status: 'active' },
        attributes: [
          'platform',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['platform'],
        raw: true
      });

      const stats = {
        totalVideos: parseInt(totalStats.totalVideos) || 0,
        totalPlays: parseInt(totalStats.totalPlays) || 0,
        totalLikes: parseInt(totalStats.totalLikes) || 0,
        totalComments: parseInt(totalStats.totalComments) || 0,
        totalShares: parseInt(totalStats.totalShares) || 0,
        platformStats: platformStats.reduce((acc, item) => {
          acc[item.platform] = parseInt(item.count);
          return acc;
        }, {})
      };

      ResponseUtil.success(ctx, stats, '获取统计信息成功');
    } catch (error) {
      console.error('获取统计信息失败:', error);
      ResponseUtil.error(ctx, '获取统计信息失败', 500);
    }
  }

  /**
   * @swagger
   * /api/author-videos/xiaohongshu/note/{noteId}:
   *   get:
   *     summary: 获取小红书帖子详情并更新数据
   *     tags: [达人作品库]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: noteId
   *         required: true
   *         schema:
   *           type: string
   *         description: 小红书帖子ID
   *     responses:
   *       200:
   *         description: 获取帖子详情成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 获取帖子详情成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     noteLink:
   *                       type: string
   *                       description: 帖子链接
   *                     updated:
   *                       type: boolean
   *                       description: 是否更新了数据
   *                     noteData:
   *                       type: object
   *                       description: 帖子详情数据
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getXiaohongshuNoteDetail(ctx) {
    try {
      const { noteId } = ctx.params;

      if (!noteId) {
        return ResponseUtil.error(ctx, '帖子ID不能为空', 400);
      }

      console.log(`🔍 开始获取小红书帖子详情: ${noteId}`);

      // 获取小红书Cookie
      const cookieManager = new CookieManager();
      const cookie = await cookieManager.getAvailableCookie('xiaohongshu');

      if (!cookie) {
        return ResponseUtil.error(ctx, '没有可用的小红书Cookie，无法获取帖子详情', 400);
      }

      // 调用小红书API获取帖子详情
      const noteDetail = await AuthorVideoController.fetchXiaohongshuNoteDetail(noteId, cookie);

      if (!noteDetail) {
        return ResponseUtil.error(ctx, '获取帖子详情失败', 500);
      }

      // 更新或创建帖子数据
      const updateResult = await AuthorVideoController.updateOrCreateNoteData(noteDetail);

      const result = {
        noteLink: noteDetail.noteLink,
        updated: updateResult.updated,
        noteData: {
          title: noteDetail.title,
          author: noteDetail.userInfo?.nickName,
          stats: {
            likeNum: noteDetail.likeNum,
            favNum: noteDetail.favNum,
            cmtNum: noteDetail.cmtNum,
            readNum: noteDetail.readNum,
            shareNum: noteDetail.shareNum
          }
        }
      };

      console.log(`✅ 小红书帖子详情获取成功: ${noteId}`);
      ResponseUtil.success(ctx, result, '获取帖子详情成功');
    } catch (error) {
      console.error('获取小红书帖子详情失败:', error);
      ResponseUtil.error(ctx, '获取帖子详情失败', 500);
    }
  }

  /**
   * 调用小红书API获取帖子详情
   * @param {string} noteId 帖子ID
   * @param {Object} cookie Cookie对象
   * @returns {Object} 帖子详情数据
   */
  static async fetchXiaohongshuNoteDetail(noteId, cookie) {
    try {
      const url = `https://pgy.xiaohongshu.com/api/solar/note/${noteId}/detail`;

      const config = {
        method: 'GET',
        url: url,
        headers: {
          'User-Agent': cookie.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Content-Type': 'application/json',
          'Referer': 'https://pgy.xiaohongshu.com',
          'Origin': 'https://pgy.xiaohongshu.com',
          'Cookie': cookie.cookieString
        },
        timeout: 15000,
        params: {
          bizCode: ''
        }
      };

      console.log(`🚀 调用小红书API: ${url}`);
      const response = await axios(config);

      if (response.data && response.data.success && response.data.data) {
        console.log(`✅ 小红书API调用成功: ${noteId}`);
        return response.data.data;
      } else {
        console.error('小红书API返回错误:', response.data);
        throw new Error(`API返回错误: ${response.data?.msg || '未知错误'}`);
      }
    } catch (error) {
      console.error(`调用小红书API失败 (${noteId}):`, error.message);
      throw error;
    }
  }

  /**
   * 更新或创建帖子数据
   * @param {Object} noteDetail 帖子详情数据
   * @returns {Object} 更新结果
   */
  static async updateOrCreateNoteData(noteDetail) {
    try {
      const {
        noteId,
        userId,
        title,
        content,
        imagesList,
        videoInfo,
        time,
        likeNum,
        favNum,
        cmtNum,
        readNum,
        shareNum,
        userInfo
      } = noteDetail;

      // 查找是否已存在该帖子
      const existingVideo = await AuthorVideo.findOne({
        where: {
          platform: 'xiaohongshu',
          platformUserId: userId,
          videoId: noteId
        }
      });

      // 构建视频数据
      const videoData = {
        authorNickname: userInfo?.nickName || null,
        platform: 'xiaohongshu',
        platformUserId: userId,
        videoId: noteId,
        title: title || '无标题',
        videoUrl: noteDetail.noteLink || null,
        videoCover: imagesList?.[0]?.url || videoInfo?.thumbnail || null,
        duration: videoInfo?.meta?.duration || 0,
        publishTime: time?.createTime ? new Date(time.createTime) : null,
        likeCount: likeNum || 0,
        playCount: readNum || 0,
        shareCount: shareNum || 0,
        commentCount: cmtNum || 0,
        collectCount: favNum || 0,
        description: content || null,
        rawData: noteDetail,
        status: 'active',
        lastUpdated: new Date()
      };

      let updated = false;

      if (existingVideo) {
        // 更新现有数据
        await existingVideo.update(videoData);
        updated = true;
        console.log(`🔄 更新帖子数据: ${title} (${noteId})`);
      } else {
        // 创建新数据
        await AuthorVideo.create(videoData);
        updated = true;
        console.log(`✨ 创建帖子数据: ${title} (${noteId})`);
      }

      return { updated };
    } catch (error) {
      console.error('更新帖子数据失败:', error);
      throw error;
    }
  }
}

module.exports = AuthorVideoController;
