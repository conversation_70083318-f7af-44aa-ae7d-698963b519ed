/**
 * 用户账号管理控制器
 *
 * 功能说明：
 * - 提供用户账号的CRUD操作接口
 * - 支持用户列表查询、分页、搜索、排序
 * - 支持用户账号的创建、更新、删除操作
 * - 支持密码重置和角色管理功能
 * - 所有接口都需要管理员权限
 *
 * 权限控制：
 * - 所有接口都需要通过authenticate中间件验证身份
 * - 所有接口都需要通过requireAdmin中间件验证管理员权限
 * - 确保只有管理员可以管理用户账号
 *
 * 接口列表：
 * - GET /api/users - 获取用户列表（支持分页、搜索、排序）
 * - POST /api/users - 创建新用户账号
 * - GET /api/users/:id - 获取指定用户详情
 * - PUT /api/users/:id - 更新用户信息
 * - DELETE /api/users/:id - 删除用户账号
 * - PUT /api/users/:id/reset-password - 重置用户密码
 * - PUT /api/users/:id/status - 更新用户状态
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { User } = require('../models');
const ResponseUtil = require('../utils/response');
const ValidationUtil = require('../utils/validation');
const { Op } = require('sequelize');

class UserController {
  /**
   * @swagger
   * /api/users:
   *   get:
   *     summary: 获取用户列表
   *     description: 获取系统中所有用户的列表，支持分页、搜索和排序功能
   *     tags: [用户管理]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: keyword
   *         schema:
   *           type: string
   *         description: 搜索关键词（用户名或邮箱）
   *       - in: query
   *         name: role
   *         schema:
   *           type: string
   *           enum: [admin, user]
   *         description: 用户角色筛选
   *       - in: query
   *         name: status
   *         schema:
   *           type: string
   *           enum: [active, inactive]
   *         description: 用户状态筛选
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           enum: [username, email, role, status, createdAt, lastLoginAt]
   *           default: createdAt
   *         description: 排序字段
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [ASC, DESC]
   *           default: DESC
   *         description: 排序方向
   *     responses:
   *       200:
   *         description: 获取用户列表成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 获取用户列表成功
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/User'
   *                 pagination:
   *                   $ref: '#/components/schemas/Pagination'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getUsers(ctx) {
    try {
      const { page = 1, limit = 10, keyword, role, status, sortBy = 'createdAt', sortOrder = 'DESC' } = ctx.query;

      const offset = (page - 1) * limit;
      const where = {};

      // 构建搜索条件
      if (keyword) {
        where[Op.or] = [
          { username: { [Op.like]: `%${keyword}%` } },
          { email: { [Op.like]: `%${keyword}%` } },
          { chineseName: { [Op.like]: `%${keyword}%` } }
        ];
      }

      // 角色筛选
      if (role) {
        where.role = role;
      }

      // 状态筛选
      if (status) {
        where.status = status;
      }

      // 查询用户列表
      const result = await User.findAndCountAll({
        where,
        attributes: [
          'id',
          'username',
          'email',
          'chineseName',
          'crmUserId',
          'crmUserPicUrl',
          'crmDepartment',
          'crmDepartmentName',
          'crmDataId',
          'role',
          'status',
          'lastLoginAt',
          'createdAt',
          'updatedAt'
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      ResponseUtil.sequelizePagination(ctx, result, { page, limit }, '获取用户列表成功');
    } catch (error) {
      console.error('获取用户列表失败:', error);
      ResponseUtil.error(ctx, '获取用户列表失败', 500);
    }
  }

  /**
   * @swagger
   * /api/users:
   *   post:
   *     summary: 创建新用户
   *     description: 管理员创建新的用户账号
   *     tags: [用户管理]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - username
   *               - email
   *               - password
   *             properties:
   *               username:
   *                 type: string
   *                 minLength: 3
   *                 maxLength: 20
   *                 pattern: '^[a-zA-Z0-9_]+$'
   *                 description: 用户名（3-20位字母、数字、下划线）
   *                 example: newuser
   *               email:
   *                 type: string
   *                 format: email
   *                 description: 邮箱地址
   *                 example: <EMAIL>
   *               password:
   *                 type: string
   *                 minLength: 8
   *                 description: 密码（至少8位，包含字母和数字）
   *                 example: password123
   *               role:
   *                 type: string
   *                 enum: [admin, user]
   *                 default: user
   *                 description: 用户角色
   *               status:
   *                 type: string
   *                 enum: [active, inactive]
   *                 default: active
   *                 description: 用户状态
   *     responses:
   *       201:
   *         description: 用户创建成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 用户创建成功
   *                 data:
   *                   $ref: '#/components/schemas/User'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       409:
   *         description: 用户名或邮箱已存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 用户名已存在
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async createUser(ctx) {
    try {
      const { username, email, chineseName, password, role = 'user', status = 'active' } = ctx.request.body;

      // 验证必填字段
      const requiredErrors = ValidationUtil.validateRequired({ username, email, password }, [
        'username',
        'email',
        'password'
      ]);
      if (requiredErrors) {
        return ResponseUtil.validationError(ctx, requiredErrors);
      }

      // 验证数据格式
      const errors = {};
      if (!ValidationUtil.isValidUsername(username)) {
        errors.username = '用户名格式不正确（3-20位字母、数字、下划线）';
      }
      if (!ValidationUtil.isValidEmail(email)) {
        errors.email = '邮箱格式不正确';
      }
      if (!ValidationUtil.isValidPassword(password)) {
        errors.password = '密码格式不正确（至少8位，包含字母和数字）';
      }
      if (role && !['admin', 'user'].includes(role)) {
        errors.role = '用户角色无效';
      }
      if (status && !['active', 'inactive'].includes(status)) {
        errors.status = '用户状态无效';
      }

      if (Object.keys(errors).length > 0) {
        return ResponseUtil.validationError(ctx, errors);
      }

      // 检查用户名和邮箱是否已存在
      const existingUser = await User.findOne({
        where: {
          [Op.or]: [{ username }, { email }]
        }
      });

      if (existingUser) {
        if (existingUser.username === username) {
          return ResponseUtil.error(ctx, '用户名已存在', 409);
        }
        if (existingUser.email === email) {
          return ResponseUtil.error(ctx, '邮箱已存在', 409);
        }
      }

      // 创建用户
      const user = await User.create({
        username,
        email,
        chineseName,
        password,
        role,
        status
      });

      ctx.status = 201;
      ResponseUtil.success(ctx, user.toSafeJSON(), '用户创建成功');
    } catch (error) {
      console.error('创建用户失败:', error);
      ResponseUtil.error(ctx, '创建用户失败', 500);
    }
  }

  /**
   * @swagger
   * /api/users/{id}:
   *   get:
   *     summary: 获取用户详情
   *     description: 根据用户ID获取指定用户的详细信息
   *     tags: [用户管理]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     responses:
   *       200:
   *         description: 获取用户详情成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 获取用户详情成功
   *                 data:
   *                   $ref: '#/components/schemas/User'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getUser(ctx) {
    try {
      const { id } = ctx.params;

      const user = await User.findByPk(id, {
        attributes: [
          'id',
          'username',
          'email',
          'chineseName',
          'crmUserId',
          'crmUserPicUrl',
          'crmDepartment',
          'crmDepartmentName',
          'crmDataId',
          'role',
          'status',
          'lastLoginAt',
          'createdAt',
          'updatedAt'
        ]
      });

      if (!user) {
        return ResponseUtil.notFound(ctx, '用户不存在');
      }

      ResponseUtil.success(ctx, user, '获取用户详情成功');
    } catch (error) {
      console.error('获取用户详情失败:', error);
      ResponseUtil.error(ctx, '获取用户详情失败', 500);
    }
  }

  /**
   * @swagger
   * /api/users/{id}:
   *   put:
   *     summary: 更新用户信息
   *     description: 更新指定用户的基本信息（不包括密码）
   *     tags: [用户管理]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               username:
   *                 type: string
   *                 minLength: 3
   *                 maxLength: 20
   *                 pattern: '^[a-zA-Z0-9_]+$'
   *                 description: 用户名（3-20位字母、数字、下划线）
   *               email:
   *                 type: string
   *                 format: email
   *                 description: 邮箱地址
   *               role:
   *                 type: string
   *                 enum: [admin, user]
   *                 description: 用户角色
   *               status:
   *                 type: string
   *                 enum: [active, inactive]
   *                 description: 用户状态
   *     responses:
   *       200:
   *         description: 用户信息更新成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 用户信息更新成功
   *                 data:
   *                   $ref: '#/components/schemas/User'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: 用户名或邮箱已存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async updateUser(ctx) {
    try {
      const { id } = ctx.params;
      const { username, email, chineseName, role, status } = ctx.request.body;

      // 查找用户
      const user = await User.findByPk(id);
      if (!user) {
        return ResponseUtil.notFound(ctx, '用户不存在');
      }

      // 验证数据格式
      const errors = {};
      if (username && !ValidationUtil.isValidUsername(username)) {
        errors.username = '用户名格式不正确（3-20位字母、数字、下划线）';
      }
      if (email && !ValidationUtil.isValidEmail(email)) {
        errors.email = '邮箱格式不正确';
      }
      if (role && !['admin', 'user'].includes(role)) {
        errors.role = '用户角色无效';
      }
      if (status && !['active', 'inactive'].includes(status)) {
        errors.status = '用户状态无效';
      }

      if (Object.keys(errors).length > 0) {
        return ResponseUtil.validationError(ctx, errors);
      }

      // 检查用户名和邮箱是否已被其他用户使用
      if (username || email) {
        const whereConditions = [];
        if (username) whereConditions.push({ username });
        if (email) whereConditions.push({ email });

        const existingUser = await User.findOne({
          where: {
            [Op.and]: [
              { id: { [Op.ne]: id } }, // 排除当前用户
              { [Op.or]: whereConditions }
            ]
          }
        });

        if (existingUser) {
          if (existingUser.username === username) {
            return ResponseUtil.error(ctx, '用户名已存在', 409);
          }
          if (existingUser.email === email) {
            return ResponseUtil.error(ctx, '邮箱已存在', 409);
          }
        }
      }

      // 更新用户信息
      const updateData = {};
      if (username !== undefined) updateData.username = username;
      if (email !== undefined) updateData.email = email;
      if (chineseName !== undefined) updateData.chineseName = chineseName;
      if (role !== undefined) updateData.role = role;
      if (status !== undefined) updateData.status = status;

      await user.update(updateData);

      ResponseUtil.success(ctx, user.toSafeJSON(), '用户信息更新成功');
    } catch (error) {
      console.error('更新用户信息失败:', error);
      ResponseUtil.error(ctx, '更新用户信息失败', 500);
    }
  }

  /**
   * @swagger
   * /api/users/{id}:
   *   delete:
   *     summary: 删除用户
   *     description: 删除指定的用户账号（不能删除自己）
   *     tags: [用户管理]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     responses:
   *       200:
   *         description: 用户删除成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 用户删除成功
   *       400:
   *         description: 不能删除自己的账号
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 不能删除自己的账号
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async deleteUser(ctx) {
    try {
      const { id } = ctx.params;
      const currentUser = ctx.state.user;

      // 不能删除自己的账号
      if (parseInt(id) === currentUser.id) {
        return ResponseUtil.error(ctx, '不能删除自己的账号', 400);
      }

      // 查找用户
      const user = await User.findByPk(id);
      if (!user) {
        return ResponseUtil.notFound(ctx, '用户不存在');
      }

      // 删除用户
      await user.destroy();

      ResponseUtil.success(ctx, null, '用户删除成功');
    } catch (error) {
      console.error('删除用户失败:', error);
      ResponseUtil.error(ctx, '删除用户失败', 500);
    }
  }

  /**
   * @swagger
   * /api/users/{id}/reset-password:
   *   put:
   *     summary: 重置用户密码
   *     description: 管理员重置指定用户的密码
   *     tags: [用户管理]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - newPassword
   *             properties:
   *               newPassword:
   *                 type: string
   *                 minLength: 8
   *                 description: 新密码（至少8位，包含字母和数字）
   *                 example: newpassword123
   *     responses:
   *       200:
   *         description: 密码重置成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: 密码重置成功
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async resetPassword(ctx) {
    try {
      const { id } = ctx.params;
      const { newPassword } = ctx.request.body;

      // 验证必填字段
      if (!newPassword) {
        return ResponseUtil.validationError(ctx, { newPassword: '新密码是必填字段' });
      }

      // 验证密码格式
      if (!ValidationUtil.isValidPassword(newPassword)) {
        return ResponseUtil.validationError(ctx, {
          newPassword: '密码格式不正确（至少8位，包含字母和数字）'
        });
      }

      // 查找用户
      const user = await User.findByPk(id);
      if (!user) {
        return ResponseUtil.notFound(ctx, '用户不存在');
      }

      // 更新密码
      await user.update({ password: newPassword });

      ResponseUtil.success(ctx, null, '密码重置成功');
    } catch (error) {
      console.error('重置密码失败:', error);
      ResponseUtil.error(ctx, '重置密码失败', 500);
    }
  }

  /**
   * 绑定CRM用户
   */
  static async bindCrmUser(ctx) {
    try {
      const { id } = ctx.params;
      const { crmUserId, crmUserPicUrl, crmDepartment, crmDepartmentName, crmDataId } = ctx.request.body;

      // 验证必填字段
      if (!crmUserId) {
        return ResponseUtil.validationError(ctx, { crmUserId: 'CRM用户ID是必填字段' });
      }

      // 查找用户
      const user = await User.findByPk(id);
      if (!user) {
        return ResponseUtil.notFound(ctx, '用户不存在');
      }

      // 更新CRM绑定信息
      await user.update({
        crmUserId,
        crmUserPicUrl,
        crmDepartment,
        crmDepartmentName,
        crmDataId
      });

      ResponseUtil.success(ctx, user.toSafeJSON(), 'CRM用户绑定成功');
    } catch (error) {
      console.error('绑定CRM用户失败:', error);
      ResponseUtil.error(ctx, '绑定CRM用户失败', 500);
    }
  }

  /**
   * 解绑CRM用户
   */
  static async unbindCrmUser(ctx) {
    try {
      const { id } = ctx.params;

      // 查找用户
      const user = await User.findByPk(id);
      if (!user) {
        return ResponseUtil.notFound(ctx, '用户不存在');
      }

      // 清除CRM绑定信息
      await user.update({
        crmUserId: null,
        crmUserPicUrl: null,
        crmDepartment: null,
        crmDepartmentName: null,
        crmDataId: null
      });

      ResponseUtil.success(ctx, user.toSafeJSON(), 'CRM用户解绑成功');
    } catch (error) {
      console.error('解绑CRM用户失败:', error);
      ResponseUtil.error(ctx, '解绑CRM用户失败', 500);
    }
  }
}

module.exports = UserController;
