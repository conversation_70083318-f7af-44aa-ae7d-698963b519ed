/**
 * 合作对接管理模型
 *
 * 功能说明：
 * - 管理合作对接记录的数据模型
 * - 包含客户信息、协议信息和CRM集成功能
 * - 支持与外部CRM系统的双向数据同步
 * - 保留原有的笔记链接解析和定时拉取功能
 *
 * 主要字段：
 * - 客户信息模块：客户名称、主页链接、所属公海、种草平台等
 * - 协议信息模块：合作前信息（标题、合作形式、金额等）和发布后登记（链接、数据等）
 * - CRM集成字段：外部客户ID、外部协议ID、关联状态
 * - 原有字段：笔记相关、数据指标等（保持向后兼容）
 *
 * <AUTHOR>
 * @version 2.0.0
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CooperationManagement = sequelize.define('CooperationManagement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },

  // ==================== 客户信息模块 ====================
  customerName: {
    type: DataTypes.STRING(100),
    field: 'customer_name',
    allowNull: false,
    comment: '客户名称'
  },
  customerHomepage: {
    type: DataTypes.TEXT,
    field: 'customer_homepage',
    allowNull: true,
    comment: '主页链接'
  },
  customerPublicSea: {
    type: DataTypes.STRING(100),
    field: 'customer_public_sea',
    allowNull: true,
    defaultValue: 'fd18e0d6b1164e7080f0fa91dc43b0d8',
    comment: '所属公海'
  },
  seedingPlatform: {
    type: DataTypes.STRING(100),
    field: 'seeding_platform',
    allowNull: true,
    comment: '种草平台'
  },
  bloggerFansCount: {
    type: DataTypes.STRING(100),
    field: 'blogger_fans_count',
    allowNull: true,
    comment: '博主粉丝量'
  },
  influencerPlatformId: {
    type: DataTypes.STRING(100),
    field: 'influencer_platform_id',
    allowNull: true,
    comment: '达人平台ID（抖音填星图ID，小红书填UID）'
  },
  bloggerWechatAndNotes: {
    type: DataTypes.TEXT,
    field: 'blogger_wechat_and_notes',
    allowNull: true,
    comment: '博主微信以及其他备注'
  },

  // ==================== 协议信息模块 - 第一部分（合作前信息） ====================
  title: {
    type: DataTypes.STRING(200),
    field: 'title',
    allowNull: false,
    comment: '标题（格式：平台-达人昵称-约稿日）'
  },
  cooperationForm: {
    type: DataTypes.STRING(100),
    field: 'cooperation_form',
    allowNull: true,
    comment: '合作形式（后端字典下拉选择）'
  },
  publishPlatform: {
    type: DataTypes.STRING(100),
    field: 'publish_platform',
    allowNull: true,
    comment: '发布平台'
  },
  cooperationBrand: {
    type: DataTypes.STRING(100),
    field: 'cooperation_brand',
    allowNull: true,
    comment: '合作品牌（后端字典下拉选择）'
  },
  cooperationProduct: {
    type: DataTypes.STRING(200),
    field: 'cooperation_product',
    allowNull: true,
    comment: '合作产品'
  },
  cooperationNotes: {
    type: DataTypes.TEXT,
    field: 'cooperation_notes',
    allowNull: true,
    comment: '备注（如坑几、返点金额等）'
  },
  scheduledPublishTime: {
    type: DataTypes.DATE,
    field: 'scheduled_publish_time',
    allowNull: true,
    comment: '约定发布时间'
  },
  cooperationAmount: {
    type: DataTypes.STRING(100),
    field: 'cooperation_amount',
    allowNull: true,
    comment: '合作金额'
  },
  influencerCommissionRate: {
    type: DataTypes.STRING(50),
    field: 'influencer_commission_rate',
    allowNull: true,
    comment: '达人佣金比例（可选）'
  },
  payeeName: {
    type: DataTypes.STRING(100),
    field: 'payee_name',
    allowNull: true,
    comment: '收款人姓名'
  },
  bankAccount: {
    type: DataTypes.STRING(100),
    field: 'bank_account',
    allowNull: true,
    comment: '银行账号'
  },
  bankName: {
    type: DataTypes.STRING(200),
    field: 'bank_name',
    allowNull: true,
    comment: '开户行'
  },
  rebateCompleted: {
    type: DataTypes.STRING(50),
    field: 'rebate_completed',
    allowNull: true,
    comment: '如有返点是否完成（后端字典下拉选择）'
  },

  // ==================== 协议信息模块 - 第二部分（发布后登记） ====================
  publishLink: {
    type: DataTypes.TEXT,
    field: 'publish_link',
    allowNull: true,
    comment: '发布链接（要求长链接）'
  },
  actualPublishDate: {
    type: DataTypes.DATE,
    field: 'actual_publish_date',
    allowNull: true,
    comment: '实际发布日期'
  },
  dataRegistrationDate: {
    type: DataTypes.DATE,
    field: 'data_registration_date',
    allowNull: true,
    comment: '数据登记日期（发布十日内登记）'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    field: 'view_count',
    allowNull: true,
    comment: '观看量（数字类型，用于帖子详情获取）'
  },
  likeCount: {
    type: DataTypes.INTEGER,
    field: 'like_count',
    allowNull: true,
    comment: '点赞数（数字类型，用于帖子详情获取）'
  },
  collectCount: {
    type: DataTypes.INTEGER,
    field: 'collect_count',
    allowNull: true,
    comment: '收藏数（数字类型，用于帖子详情获取）'
  },
  commentCount: {
    type: DataTypes.INTEGER,
    field: 'comment_count',
    allowNull: true,
    comment: '评论数（数字类型，用于帖子详情获取）'
  },
  contentImplantCoefficient: {
    type: DataTypes.STRING(50),
    field: 'content_implant_coefficient',
    allowNull: true,
    comment: '内容植入系数（后端字典下拉选择）'
  },
  commentMaintenanceCoefficient: {
    type: DataTypes.STRING(50),
    field: 'comment_maintenance_coefficient',
    allowNull: true,
    comment: '评论区维护系数（后端字典下拉选择）'
  },
  brandTopicIncluded: {
    type: DataTypes.STRING(50),
    field: 'brand_topic_included',
    allowNull: true,
    comment: '是否加入品牌话题或产品词（后端字典下拉选择）'
  },
  selfEvaluation: {
    type: DataTypes.STRING(100),
    field: 'self_evaluation',
    allowNull: true,
    comment: '自评（付费笔记与置换爆文自评，后端字典下拉选择）'
  },

  // ==================== CRM集成关联字段 ====================
  externalCustomerId: {
    type: DataTypes.STRING(100),
    field: 'external_customer_id',
    allowNull: true,
    comment: '外部客户ID（关联CRM系统客户）'
  },
  externalAgreementId: {
    type: DataTypes.STRING(100),
    field: 'external_agreement_id',
    allowNull: true,
    comment: '外部协议ID（关联CRM系统协议）'
  },
  crmLinkStatus: {
    type: DataTypes.ENUM('unlinked', 'customer_linked', 'fully_linked'),
    field: 'crm_link_status',
    defaultValue: 'unlinked',
    comment: 'CRM关联状态（未关联/客户已关联/完全关联）'
  },

  // ==================== 达人提报关联字段 ====================
  influencerReportId: {
    type: DataTypes.INTEGER,
    field: 'influencer_report_id',
    allowNull: true,
    comment: '关联的达人提报记录ID'
  },

  // ==================== 保留原有字段（向后兼容） ====================
  cooperationMonth: {
    type: DataTypes.STRING(20),
    field: 'cooperation_month',
    allowNull: true,
    comment: '合作月份，格式：YYYY-MM（保留字段）'
  },
  platform: {
    type: DataTypes.ENUM('xiaohongshu', 'douyin', 'weibo', 'bilibili'),
    allowNull: true,
    comment: '平台类型（保留字段）'
  },
  bloggerName: {
    type: DataTypes.STRING(100),
    field: 'blogger_name',
    allowNull: true,
    comment: '博主名称（保留字段）'
  },
  responsiblePerson: {
    type: DataTypes.STRING(50),
    field: 'responsible_person',
    allowNull: true,
    comment: '负责人花名（保留字段）'
  },
  influencerHomepage: {
    type: DataTypes.TEXT,
    field: 'influencer_homepage',
    allowNull: true,
    comment: '达人平台主页链接（保留字段）'
  },
  cooperationNoteLink: {
    type: DataTypes.TEXT,
    field: 'cooperation_note_link',
    allowNull: true,
    comment: '合作笔记链接（保留字段）'
  },
  cooperationPrice: {
    type: DataTypes.STRING(200),
    field: 'cooperation_price',
    allowNull: true,
    comment: '合作价格（支持文本描述，保留字段）'
  },
  contentDirection: {
    type: DataTypes.STRING(200),
    field: 'content_direction',
    allowNull: true,
    comment: '内容方向（保留字段）'
  },
  // 注释掉重复字段，避免数据库冲突
  // oldCooperationProduct: {
  //   type: DataTypes.STRING(200),
  //   field: 'old_cooperation_product',
  //   allowNull: true,
  //   comment: '合作品（保留字段，重命名避免冲突）'
  // },
  // oldScheduledPublishTime: {
  //   type: DataTypes.STRING(100),
  //   field: 'old_scheduled_publish_time',
  //   allowNull: true,
  //   comment: '约定发布时间（字符串格式，保留字段）'
  // },
  workProgress: {
    type: DataTypes.STRING(200),
    field: 'work_progress',
    allowNull: true,
    comment: '工作进度（自由输入）'
  },
  noteId: {
    type: DataTypes.STRING(100),
    field: 'note_id',
    allowNull: true,
    comment: '笔记ID（从链接自动解析）'
  },
  noteLinkUpdateTime: {
    type: DataTypes.STRING(50),
    field: 'note_link_update_time',
    allowNull: true,
    comment: '笔记链接更新时间'
  },
  scheduledFetchTime: {
    type: DataTypes.STRING(50),
    field: 'scheduled_fetch_time',
    allowNull: true,
    comment: '定时拉取笔记数据时间（更新时间+10天上午9点）'
  },
  // 笔记数据指标字段
  noteData: {
    type: DataTypes.JSON,
    field: 'note_data',
    allowNull: true,
    comment: '完整的笔记数据（API返回的原始数据）'
  },
  impNum: {
    type: DataTypes.INTEGER,
    field: 'imp_num',
    allowNull: true,
    comment: '曝光数'
  },
  likeNum: {
    type: DataTypes.INTEGER,
    field: 'like_num',
    allowNull: true,
    comment: '点赞数'
  },
  favNum: {
    type: DataTypes.INTEGER,
    field: 'fav_num',
    allowNull: true,
    comment: '收藏数'
  },
  cmtNum: {
    type: DataTypes.INTEGER,
    field: 'cmt_num',
    allowNull: true,
    comment: '评论数'
  },
  readNum: {
    type: DataTypes.INTEGER,
    field: 'read_num',
    allowNull: true,
    comment: '阅读数'
  },
  shareNum: {
    type: DataTypes.INTEGER,
    field: 'share_num',
    allowNull: true,
    comment: '分享数'
  },
  followCnt: {
    type: DataTypes.INTEGER,
    field: 'follow_cnt',
    allowNull: true,
    comment: '关注数'
  },
  dataFetchStatus: {
    type: DataTypes.ENUM('pending', 'fetching', 'success', 'failed'),
    field: 'data_fetch_status',
    defaultValue: 'pending',
    comment: '数据拉取状态'
  },
  dataFetchTime: {
    type: DataTypes.STRING(50),
    field: 'data_fetch_time',
    allowNull: true,
    comment: '数据拉取时间'
  },
  dataFetchError: {
    type: DataTypes.TEXT,
    field: 'data_fetch_error',
    allowNull: true,
    comment: '数据拉取错误信息'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    allowNull: true,
    comment: '创建人ID'
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    field: 'updated_by',
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'cooperation_management',
  timestamps: true,
  underscored: true,
  comment: '合作对接管理表'
});

module.exports = CooperationManagement;
