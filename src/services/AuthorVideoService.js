/**
 * 达人视频作品服务
 * 专门处理达人视频数据的保存、更新、查询等操作
 *
 * 功能特性：
 * - 批量保存视频数据
 * - 重复数据处理（upsert操作）
 * - 事务安全性保证
 * - 完整的错误处理和日志记录
 * - 独立的视频保存功能（不依赖达人数据）
 * - 通过星图ID实现松耦合的数据关联
 * - 支持功能开关控制
 *
 * <AUTHOR>
 * @version 2.0.0
 */

const { AuthorVideo, sequelize } = require('../models');
const { Op } = require('sequelize');

class AuthorVideoService {
  constructor() {
    this.batchSize = 50; // 批量处理大小
    this.logPrefix = '[AuthorVideoService]'; // 日志前缀
  }

  /**
   * 记录信息日志
   * @param {string} message 日志消息
   * @param {Object} data 附加数据
   */
  logInfo(message, data = null) {
    if (data) {
      console.log(`${this.logPrefix} ℹ️ ${message}`, data);
    } else {
      console.log(`${this.logPrefix} ℹ️ ${message}`);
    }
  }

  /**
   * 记录成功日志
   * @param {string} message 日志消息
   * @param {Object} data 附加数据
   */
  logSuccess(message, data = null) {
    if (data) {
      console.log(`${this.logPrefix} ✅ ${message}`, data);
    } else {
      console.log(`${this.logPrefix} ✅ ${message}`);
    }
  }

  /**
   * 记录警告日志
   * @param {string} message 日志消息
   * @param {Object} data 附加数据
   */
  logWarning(message, data = null) {
    if (data) {
      console.warn(`${this.logPrefix} ⚠️ ${message}`, data);
    } else {
      console.warn(`${this.logPrefix} ⚠️ ${message}`);
    }
  }

  /**
   * 记录错误日志
   * @param {string} message 日志消息
   * @param {Error|Object} error 错误对象或附加数据
   */
  logError(message, error = null) {
    if (error) {
      console.error(`${this.logPrefix} ❌ ${message}`, error);
    } else {
      console.error(`${this.logPrefix} ❌ ${message}`);
    }
  }

  /**
   * 处理单个批次的视频数据
   * @param {number} authorId 达人ID
   * @param {string} platformUserId 平台用户ID
   * @param {string} platform 平台类型
   * @param {Array} videos 视频数据数组
   * @param {number} crawlTaskId 爬虫任务ID
   * @param {boolean} forceUpdate 是否强制更新
   * @param {Object} transaction 事务对象
   * @returns {Object} 批次处理结果
   */
  async processBatch(authorId, platformUserId, platform, videos, crawlTaskId, forceUpdate, transaction) {
    const batchResults = {
      success: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: []
    };

    for (const video of videos) {
      try {
        const result = await this.saveOrUpdateVideo(
          authorId,
          platformUserId,
          platform,
          video,
          crawlTaskId,
          forceUpdate,
          transaction
        );

        batchResults.success++;
        if (result.created) {
          batchResults.created++;
        } else {
          batchResults.updated++;
        }
      } catch (error) {
        batchResults.failed++;
        batchResults.errors.push({
          videoId: video.videoId,
          title: video.title,
          error: error.message
        });
        console.error(`❌ 保存视频失败 ${video.videoId}:`, error.message);
      }
    }

    return batchResults;
  }

  /**
   * 保存或更新单个视频数据
   * @param {number} authorId 达人ID
   * @param {string} platformUserId 平台用户ID
   * @param {string} platform 平台类型
   * @param {Object} video 视频数据
   * @param {number} crawlTaskId 爬虫任务ID
   * @param {boolean} forceUpdate 是否强制更新
   * @param {Object} transaction 事务对象
   * @returns {Object} 保存结果
   */
  async saveOrUpdateVideo(authorId, platformUserId, platform, video, crawlTaskId, forceUpdate, transaction) {
    // 验证视频数据
    this.validateVideoData(video);

    // 检查是否已存在相同视频
    const existingVideo = await AuthorVideo.findOne({
      where: {
        platform,
        platformUserId,
        videoId: video.videoId
      },
      transaction
    });

    const videoData = this.buildVideoData(authorId, platformUserId, platform, video, crawlTaskId);

    if (existingVideo) {
      if (forceUpdate || this.shouldUpdateVideo(existingVideo, video)) {
        // 更新现有视频
        await existingVideo.update(videoData, { transaction });
        console.log(`🔄 更新视频: ${video.title} (${video.videoId})`);
        return { created: false, video: existingVideo };
      } else {
        console.log(`⏭️ 跳过视频: ${video.title} (${video.videoId}) - 数据未变化`);
        return { created: false, video: existingVideo };
      }
    } else {
      // 创建新视频
      const newVideo = await AuthorVideo.create(videoData, { transaction });
      console.log(`✨ 新增视频: ${video.title} (${video.videoId})`);
      return { created: true, video: newVideo };
    }
  }

  /**
   * 验证视频数据
   * @param {Object} video 视频数据
   */
  validateVideoData(video) {
    const errors = [];

    if (!video.videoId) {
      errors.push('视频ID不能为空');
    }

    if (!video.title) {
      errors.push('视频标题不能为空');
    }

    // 验证数值字段
    const numericFields = ['playCount', 'likeCount', 'commentCount', 'shareCount', 'duration'];
    numericFields.forEach(field => {
      if (video[field] !== undefined && video[field] !== null) {
        const value = parseInt(video[field]);
        if (isNaN(value) || value < 0) {
          errors.push(`${field} 必须是非负整数`);
        }
      }
    });

    // 验证发布时间格式
    if (video.publishTime && !this.isValidDate(video.publishTime)) {
      errors.push('发布时间格式无效');
    }

    if (errors.length > 0) {
      throw new Error(`视频数据验证失败: ${errors.join(', ')}`);
    }
  }

  /**
   * 验证日期格式
   * @param {*} date 日期值
   * @returns {boolean} 是否为有效日期
   */
  isValidDate(date) {
    try {
      const d = new Date(date);
      return d instanceof Date && !isNaN(d);
    } catch (error) {
      return false;
    }
  }

  /**
   * 构建视频数据对象
   * @param {number} authorId 达人ID
   * @param {string} platformUserId 平台用户ID
   * @param {string} platform 平台类型
   * @param {Object} video 原始视频数据
   * @param {number} crawlTaskId 爬虫任务ID
   * @returns {Object} 格式化的视频数据
   */
  buildVideoData(authorId, platformUserId, platform, video, crawlTaskId) {
    return {
      authorId,
      authorNickname: null, // 在有authorId的情况下，昵称通过关联查询获取
      platform,
      platformUserId,
      videoId: video.videoId,
      title: video.title || '无标题',
      videoUrl: video.videoUrl || null,
      videoCover: video.videoCover || null,
      duration: video.duration || 0,
      publishTime: video.publishTime ? new Date(video.publishTime) : null,
      likeCount: parseInt(video.likeCount) || 0,
      playCount: parseInt(video.playCount) || 0,
      shareCount: parseInt(video.shareCount) || 0,
      commentCount: parseInt(video.commentCount) || 0,
      collectCount: parseInt(video.collectCount) || 0,
      tags: video.tags || null,
      description: video.description || null,
      location: video.location || null,
      musicInfo: video.musicInfo || null,
      videoStats: video.videoStats || null,
      rawData: video.rawData || video,
      status: 'active',
      crawlTaskId,
      lastUpdated: new Date()
    };
  }

  /**
   * 判断是否需要更新视频数据
   * @param {Object} existingVideo 现有视频数据
   * @param {Object} newVideo 新视频数据
   * @returns {boolean} 是否需要更新
   */
  shouldUpdateVideo(existingVideo, newVideo) {
    // 检查关键数据是否有变化
    const keyFields = ['playCount', 'likeCount', 'commentCount', 'shareCount', 'title'];

    for (const field of keyFields) {
      const existingValue = existingVideo[field];
      const newValue = newVideo[field];

      if (existingValue !== newValue) {
        console.log(`📊 检测到数据变化 ${field}: ${existingValue} -> ${newValue}`);
        return true;
      }
    }

    return false;
  }

  /**
   * 根据达人ID查询视频列表
   * @param {number} authorId 达人ID
   * @param {Object} options 查询选项
   * @returns {Array} 视频列表
   */
  async getVideosByAuthor(authorId, options = {}) {
    const { limit = 20, offset = 0, orderBy = 'publishTime', orderDirection = 'DESC' } = options;

    try {
      const videos = await AuthorVideo.findAll({
        where: { authorId },
        attributes: AuthorVideo.QUERY_ATTRIBUTES,
        order: [[orderBy, orderDirection]],
        limit,
        offset
      });

      console.log(`📋 查询达人 ${authorId} 的视频: 找到 ${videos.length} 个`);
      return videos;
    } catch (error) {
      console.error(`❌ 查询达人视频失败:`, error.message);
      throw error;
    }
  }

  /**
   * 删除达人的所有视频
   * @param {number} authorId 达人ID
   * @returns {number} 删除的视频数量
   */
  async deleteVideosByAuthor(authorId) {
    try {
      const deletedCount = await AuthorVideo.destroy({
        where: { authorId }
      });

      console.log(`🗑️ 删除达人 ${authorId} 的 ${deletedCount} 个视频`);
      return deletedCount;
    } catch (error) {
      console.error(`❌ 删除达人视频失败:`, error.message);
      throw error;
    }
  }

  /**
   * 获取视频统计信息
   * @param {number} authorId 达人ID
   * @returns {Object} 统计信息
   */
  async getVideoStats(authorId) {
    try {
      // 首先获取达人信息，包括platform_id
      const { MyInfluencer } = require('../models');
      const influencer = await MyInfluencer.findByPk(authorId);

      if (!influencer) {
        console.warn(`⚠️ 达人 ${authorId} 不存在`);
        return {
          totalVideos: 0,
          totalPlays: 0,
          totalLikes: 0,
          totalComments: 0,
          totalShares: 0,
          avgPlays: 0,
          maxPlays: 0
        };
      }

      // 构建查询条件：优先使用author_id，如果没有则使用platform_user_id匹配
      const whereCondition = {
        [Op.or]: [{ authorId: authorId }, ...(influencer.platformId ? [{ platformUserId: influencer.platformId }] : [])]
      };

      console.log(`🔍 查询达人 ${authorId} (${influencer.nickname}) 的视频统计，platformId: ${influencer.platformId}`);

      const stats = await AuthorVideo.findAll({
        where: whereCondition,
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'totalVideos'],
          [sequelize.fn('SUM', sequelize.col('play_count')), 'totalPlays'],
          [sequelize.fn('SUM', sequelize.col('like_count')), 'totalLikes'],
          [sequelize.fn('SUM', sequelize.col('comment_count')), 'totalComments'],
          [sequelize.fn('SUM', sequelize.col('share_count')), 'totalShares'],
          [sequelize.fn('AVG', sequelize.col('play_count')), 'avgPlays'],
          [sequelize.fn('MAX', sequelize.col('play_count')), 'maxPlays']
        ],
        raw: true
      });

      const result = stats[0] || {};

      // 转换数据类型并处理null值
      Object.keys(result).forEach(key => {
        if (result[key] !== null && result[key] !== undefined) {
          result[key] = parseInt(result[key]) || 0;
        } else {
          result[key] = 0;
        }
      });

      console.log(`📊 达人 ${authorId} (${influencer.nickname}) 视频统计:`, result);
      return result;
    } catch (error) {
      console.error(`❌ 获取视频统计失败:`, error.message);
      throw error;
    }
  }

  /**
   * 根据爬虫任务保存视频数据
   * 专注于视频数据保存，不依赖达人数据的存在状态
   * 通过星图/小红书ID（platformUserId）实现松耦合的数据关联
   * @param {Object} authorData 达人数据
   * @param {number} crawlTaskId 爬虫任务ID
   * @param {Array} videos 视频数据数组
   * @returns {Object} 保存结果
   */
  async saveVideosFromCrawlTask(authorData, crawlTaskId, videos) {
    try {
      this.logInfo(`开始从爬虫任务保存视频数据...`, {
        platformUserId: authorData.platformUserId,
        nickname: authorData.nickname,
        crawlTaskId,
        videoCount: videos?.length || 0
      });

      // 检查功能开关 - 确保视频保存功能受配置控制
      // 注意：这里可以根据需要添加全局配置检查
      // if (!config.enableVideoSave) { return ... }

      // 检查是否有视频数据
      if (!videos || !Array.isArray(videos) || videos.length === 0) {
        this.logWarning(`达人 ${authorData.platformUserId} (${authorData.nickname}) 没有视频数据可保存`);
        return {
          total: 0,
          success: 0,
          updated: 0,
          created: 0,
          failed: 0,
          errors: []
        };
      }

      this.logInfo(`准备保存 ${videos.length} 个视频数据，达人: ${authorData.nickname} (${authorData.platformUserId})`);

      // 直接保存视频数据，不依赖达人表的存在
      const videoSaveResult = await this.batchSaveVideosWithoutAuthorDependency(authorData.platformUserId, videos, {
        platform: authorData.platform,
        authorNickname: authorData.nickname, // 传递达人昵称
        crawlTaskId,
        forceUpdate: false
      });

      this.logSuccess(`爬虫任务视频保存完成`, videoSaveResult);
      return videoSaveResult;
    } catch (error) {
      this.logError(`从爬虫任务保存视频失败`, error);
      throw error;
    }
  }

  /**
   * 批量保存视频数据（不依赖达人数据）
   * 专注于视频保存逻辑，通过星图ID实现松耦合关联
   * @param {string} platformUserId 平台用户ID（星图/小红书ID）
   * @param {Array} videos 视频数据数组
   * @param {Object} options 选项配置
   * @returns {Object} 保存结果
   */
  async batchSaveVideosWithoutAuthorDependency(platformUserId, videos, options = {}) {
    const { platform = 'juxingtu', authorNickname = null, crawlTaskId = null, forceUpdate = false } = options;

    if (!platformUserId || !Array.isArray(videos) || videos.length === 0) {
      throw new Error('参数无效：platformUserId和videos数组不能为空');
    }

    this.logInfo(`开始批量保存平台用户ID ${platformUserId} 的 ${videos.length} 个视频...`);

    const results = {
      total: videos.length,
      success: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: []
    };

    // 使用事务确保数据一致性
    const transaction = await sequelize.transaction();

    try {
      // 分批处理视频数据
      for (let i = 0; i < videos.length; i += this.batchSize) {
        const batch = videos.slice(i, i + this.batchSize);
        this.logInfo(
          `处理第 ${Math.floor(i / this.batchSize) + 1} 批视频 (${i + 1}-${Math.min(
            i + this.batchSize,
            videos.length
          )}/${videos.length})`
        );

        const batchResults = await this.processBatchWithoutAuthorDependency(
          platformUserId,
          platform,
          batch,
          crawlTaskId,
          forceUpdate,
          authorNickname,
          transaction
        );

        // 合并批次结果
        results.success += batchResults.success;
        results.updated += batchResults.updated;
        results.created += batchResults.created;
        results.failed += batchResults.failed;
        results.errors.push(...batchResults.errors);
      }

      // 提交事务
      await transaction.commit();

      this.logSuccess(
        `视频数据保存完成: 成功 ${results.success}, 新增 ${results.created}, 更新 ${results.updated}, 失败 ${results.failed}`
      );

      return results;
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      this.logError(`批量保存视频失败`, error);
      throw error;
    }
  }

  /**
   * 处理单个批次的视频数据（不依赖达人数据）
   * @param {string} platformUserId 平台用户ID
   * @param {string} platform 平台类型
   * @param {Array} videos 视频数据数组
   * @param {number} crawlTaskId 爬虫任务ID
   * @param {boolean} forceUpdate 是否强制更新
   * @param {string} authorNickname 达人昵称
   * @param {Object} transaction 事务对象
   * @returns {Object} 批次处理结果
   */
  async processBatchWithoutAuthorDependency(
    platformUserId,
    platform,
    videos,
    crawlTaskId,
    forceUpdate,
    authorNickname,
    transaction
  ) {
    const batchResults = {
      success: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: []
    };

    for (const video of videos) {
      try {
        const result = await this.saveOrUpdateVideoWithoutAuthorDependency(
          platformUserId,
          platform,
          video,
          crawlTaskId,
          forceUpdate,
          authorNickname,
          transaction
        );

        batchResults.success++;
        if (result.created) {
          batchResults.created++;
        } else {
          batchResults.updated++;
        }
      } catch (error) {
        batchResults.failed++;
        batchResults.errors.push({
          videoId: video.videoId,
          title: video.title,
          error: error.message
        });
        console.error(`❌ 保存视频失败 ${video.videoId}:`, error.message);
      }
    }

    return batchResults;
  }

  /**
   * 保存或更新单个视频数据（不依赖达人数据）
   * 使用星图ID作为关联字段，不需要本地达人表的外键关联
   * @param {string} platformUserId 平台用户ID（星图/小红书ID）
   * @param {string} platform 平台类型
   * @param {Object} video 视频数据
   * @param {number} crawlTaskId 爬虫任务ID
   * @param {boolean} forceUpdate 是否强制更新
   * @param {string} authorNickname 达人昵称
   * @param {Object} transaction 事务对象
   * @returns {Object} 保存结果
   */
  async saveOrUpdateVideoWithoutAuthorDependency(
    platformUserId,
    platform,
    video,
    crawlTaskId,
    forceUpdate,
    authorNickname,
    transaction
  ) {
    // 验证视频数据
    this.validateVideoData(video);

    // 检查是否已存在相同视频
    const existingVideo = await AuthorVideo.findOne({
      where: {
        platform,
        platformUserId,
        videoId: video.videoId
      },
      transaction
    });

    const videoData = this.buildVideoDataWithoutAuthorId(platformUserId, platform, video, crawlTaskId, authorNickname);

    if (existingVideo) {
      if (forceUpdate || this.shouldUpdateVideo(existingVideo, video)) {
        // 更新现有视频
        await existingVideo.update(videoData, { transaction });
        console.log(`🔄 更新视频: ${video.title} (${video.videoId})`);
        return { created: false, video: existingVideo };
      } else {
        console.log(`⏭️ 跳过视频: ${video.title} (${video.videoId}) - 数据未变化`);
        return { created: false, video: existingVideo };
      }
    } else {
      // 创建新视频
      const newVideo = await AuthorVideo.create(videoData, { transaction });
      console.log(`✨ 新增视频: ${video.title} (${video.videoId})`);
      return { created: true, video: newVideo };
    }
  }

  /**
   * 构建视频数据对象（不依赖达人ID）
   * 使用星图ID作为关联字段，实现松耦合的数据关联
   * @param {string} platformUserId 平台用户ID（星图/小红书ID）
   * @param {string} platform 平台类型
   * @param {Object} video 原始视频数据
   * @param {number} crawlTaskId 爬虫任务ID
   * @param {string} authorNickname 达人昵称
   * @returns {Object} 格式化的视频数据
   */
  buildVideoDataWithoutAuthorId(platformUserId, platform, video, crawlTaskId, authorNickname = null) {
    return {
      authorId: null, // 不依赖本地达人表的外键关联
      authorNickname: authorNickname || null, // 存储达人昵称
      platform,
      platformUserId, // 使用星图ID作为关联字段
      videoId: video.videoId,
      title: video.title || '无标题',
      videoUrl: video.videoUrl || null,
      videoCover: video.videoCover || null,
      duration: video.duration || 0,
      publishTime: video.publishTime ? new Date(video.publishTime) : null,
      likeCount: parseInt(video.likeCount) || parseInt(video.likeNum) || 0,
      playCount: parseInt(video.playCount) || parseInt(video.readNum) || 0,
      shareCount: parseInt(video.shareCount) || 0,
      commentCount: parseInt(video.commentCount) || 0,
      collectCount: parseInt(video.collectCount) || parseInt(video.collectNum) || 0,
      tags: video.tags || null,
      description: video.description || null,
      location: video.location || null,
      musicInfo: video.musicInfo || null,
      videoStats: video.videoStats || null,
      rawData: video.rawData || video,
      status: 'active',
      crawlTaskId,
      lastUpdated: new Date()
    };
  }
}

module.exports = new AuthorVideoService();
