/**
 * CRM数据映射服务 - 简化版
 *
 * 功能说明：
 * - 提供双向字段映射：系统字段 ↔ 外部CRM字段
 * - 简化的数据转换逻辑，专注于字段名转换
 * - 支持客户数据和协议数据的映射
 *
 * 主要功能：
 * - mapFromCrm: 从外部CRM同步数据时的字段转换
 * - mapToCrm: 更新到外部CRM时的字段转换
 * - 保持现有映射配置不变，确保兼容性
 *
 * <AUTHOR>
 * @version 2.0.0 - 简化版
 */

class CrmDataMappingService {
  constructor() {
    // ==================== 核心字段映射配置 ====================
    // 保持现有配置不变，确保向后兼容

    // 客户数据字段映射配置 (CRM字段 -> 本地字段)
    this.customerFieldMapping = {
      // CRM字段 -> 本地字段
      id: 'externalCustomerId', // 客户ID（主键）
      custom_name: 'customerName', // 客户名称
      seas_id: 'customerPublicSea', // 所属公海
      customer_textarea_11: 'customerHomepage', // 主页链接
      customer_check_box_2: 'seedingPlatform', // 种草平台
      customer_select_28: 'bloggerFansCount', // 博主粉丝量级
      customer_textarea_13: 'influencerPlatformId', // 平台ID
      custom_remark: 'bloggerWechatAndNotes', // 备注信息
      created: 'createdAt', // 创建时间
      modified: 'updatedAt' // 更新时间
    };

    // 协议数据字段映射配置
    this.agreementFieldMapping = {
      // CRM字段 -> 本地字段
      id: 'externalAgreementId', // 协议ID（主键）
      contract_title: 'title', // 协议标题
      custom_name: 'customerName', // 关联客户名称
      customer_id: 'externalCustomerId', // 关联客户ID
      contract_select_0: 'cooperationForm', // 合作形式
      contract_select_26: 'publishPlatform', // 发布平台
      contract_select_25: 'cooperationBrand', // 合作品牌
      contract_textarea_0: 'cooperationProduct', // 合作产品
      contract_textarea_1: 'cooperationNotes', // 备注
      contract_date_4: 'actualPublishDate', //实际发布日期 actualPublishDate
      contract_end_date: 'scheduledPublishTime', // 约定发布时间
      contract_amount: 'cooperationAmount', // 合作金额
      contract_percent_2: 'influencerCommissionRate', // 达人佣金比例
      contract_input_13: 'payeeName', // 收款人姓名
      contract_input_14: 'bankAccount', // 银行账号
      contract_textarea_2: 'bankName', // 开户行
      contract_select_5: 'rebateCompleted', // 返点状态
      contract_url_0: 'publishLink', // 发布链接
      contract_date_5: 'dataRegistrationDate', // 数据登记日期
      contract_integer_1: 'viewCount', // 观看量
      contract_integer_2: 'likeCount', // 点赞数
      contract_integer_3: 'collectCount', // 收藏数
      contract_integer_4: 'commentCount', // 评论数
      contract_select_6: 'brandTopicStatus', // 是否加入品牌话题
      contract_amount_8: 'storeSales', // 店铺销售额
      contract_select_2: 'selfEvaluation' // 自评
    };

    // ==================== 双向映射配置 ====================
    // 生成反向映射，用于数据更新到外部CRM

    // 本地字段 -> CRM字段 (用于更新到外部CRM)
    this.localToCustomerMapping = this.createReverseMapping(this.customerFieldMapping);
    this.localToAgreementMapping = this.createReverseMapping(this.agreementFieldMapping);

    // 为了保持向后兼容性，创建别名
    this.cooperationToAgreementMapping = this.localToAgreementMapping;
  }

  /**
   * 创建反向映射的工具方法
   * 将 {CRM字段: 本地字段} 转换为 {本地字段: CRM字段}
   * @param {Object} mapping 原始映射对象
   * @returns {Object} 反向映射对象
   */
  createReverseMapping(mapping) {
    const reversed = {};
    for (const [crmField, localField] of Object.entries(mapping)) {
      reversed[localField] = crmField;
    }
    return reversed;
  }

  // ==================== 核心映射方法 ====================

  /**
   * 从外部CRM同步数据时的字段转换
   * @param {Object} crmData 外部CRM数据
   * @param {string} dataType 数据类型 ('customer' | 'agreement')
   * @returns {Object} 转换后的本地数据
   */
  mapFromCrm(crmData, dataType) {
    try {
      const mappedData = {};
      const fieldMapping = dataType === 'customer' ? this.customerFieldMapping : this.agreementFieldMapping;

      // 基础字段映射：CRM字段 -> 本地字段
      for (const [crmField, localField] of Object.entries(fieldMapping)) {
        if (crmData[crmField] !== undefined && crmData[crmField] !== null) {
          let value = crmData[crmField];

          // 特殊处理数组字段：如果CRM返回数组，转换为字符串存储
          if (this.isArrayField(localField) && Array.isArray(value)) {
            value = this.formatArrayForCrm(value);
            console.log(`CRM数组字段转换: ${crmField} [${crmData[crmField].join(', ')}] -> ${localField} "${value}"`);
          }

          mappedData[localField] = value;
        }
      }

      console.log(`✅ ${dataType}数据从CRM映射完成`);
      return mappedData;
    } catch (error) {
      console.error(`❌ ${dataType}数据从CRM映射失败:`, error.message);
      throw new Error(`${dataType}数据从CRM映射失败: ${error.message}`);
    }
  }

  /**
   * 更新到外部CRM时的字段转换
   * @param {Object} localData 本地系统数据
   * @param {string} dataType 数据类型 ('customer' | 'agreement')
   * @returns {Object} 转换后的CRM数据
   */
  mapToCrm(localData, dataType) {
    try {
      const crmData = {};
      const fieldMapping = dataType === 'customer' ? this.localToCustomerMapping : this.localToAgreementMapping;

      // 基础字段映射：本地字段 -> CRM字段
      for (const [localField, crmField] of Object.entries(fieldMapping)) {
        // 对于数组字段，即使是null/undefined也要处理，确保转换为空字符串
        if (this.isArrayField(localField)) {
          const value = this.formatArrayForCrm(localData[localField]);
          crmData[crmField] = value;
        }
        // 对于其他字段，只有在有值时才处理
        else if (localData[localField] !== undefined && localData[localField] !== null) {
          let value = localData[localField];

          // 特殊处理日期字段
          if (this.isDateField(localField)) {
            value = this.formatDateForCrm(value);
          }

          crmData[crmField] = value;
        }
      }

      console.log(`✅ ${dataType}数据到CRM映射完成`);
      return crmData;
    } catch (error) {
      console.error(`❌ ${dataType}数据到CRM映射失败:`, error.message);
      throw new Error(`${dataType}数据到CRM映射失败: ${error.message}`);
    }
  }

  // ==================== 向后兼容方法 ====================

  /**
   * 映射客户数据 (保持向后兼容)
   * @param {Object} crmCustomerData CRM客户数据
   * @returns {Object} 映射后的客户数据
   */
  mapCustomerData(crmCustomerData) {
    return this.mapFromCrm(crmCustomerData, 'customer');
  }

  /**
   * 映射协议数据 (保持向后兼容)
   * @param {Object} crmAgreementData CRM协议数据
   * @param {string} customerName 关联的客户名称
   * @returns {Object} 映射后的协议数据
   */
  mapAgreementData(crmAgreementData, customerName) {
    const mappedData = this.mapFromCrm(crmAgreementData, 'agreement');

    // 确保客户名称正确
    if (customerName) {
      mappedData.customerName = customerName;
    }

    return mappedData;
  }

  /**
   * 将合作对接数据映射为CRM客户数据格式 (保持向后兼容)
   * @param {Object} cooperationData 合作对接数据
   * @returns {Object} CRM客户数据格式
   */
  mapCooperationToCustomer(cooperationData) {
    return this.mapToCrm(cooperationData, 'customer');
  }

  /**
   * 将合作对接数据映射为CRM协议数据格式 (保持向后兼容)
   * @param {Object} cooperationData 合作对接数据
   * @returns {Object} CRM协议数据格式
   */
  mapCooperationToAgreement(cooperationData) {
    return this.mapToCrm(cooperationData, 'agreement');
  }

  // ==================== 工具方法 ====================

  /**
   * 判断字段是否为日期字段
   * @param {string} fieldName 字段名
   * @returns {boolean} 是否为日期字段
   */
  isDateField(fieldName) {
    const dateFields = [
      'actualPublishDate',
      'scheduledPublishTime',
      'dataRegistrationTime',
      'dataRegistrationDate',
      'createdAt',
      'updatedAt'
    ];
    return dateFields.includes(fieldName);
  }

  /**
   * 判断字段是否为数组字段（需要转换为字符串）
   * @param {string} fieldName 字段名
   * @returns {boolean} 是否为数组字段
   */
  isArrayField(fieldName) {
    const arrayFields = [
      'seedingPlatform' // 种草平台可能是数组格式
    ];
    return arrayFields.includes(fieldName);
  }

  /**
   * 格式化日期为CRM接受的格式
   * @param {string|Date} dateValue 日期值
   * @returns {string} 格式化后的日期字符串
   */
  formatDateForCrm(dateValue) {
    if (!dateValue) return '';

    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) {
        console.warn(`无效的日期值: ${dateValue}`);
        return '';
      }

      // 格式化为 YYYY/MM/DD 格式（外部CRM接受的格式）
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      const formatted = `${year}/${month}/${day}`;
      console.log(`日期格式化: ${dateValue} -> ${formatted}`);
      return formatted;
    } catch (error) {
      console.error(`日期格式化失败: ${dateValue}`, error);
      return '';
    }
  }

  /**
   * 格式化数组为CRM接受的字符串格式
   * @param {Array|string} arrayValue 数组值或字符串值
   * @returns {string} 格式化后的字符串
   */
  formatArrayForCrm(arrayValue) {
    if (!arrayValue) return '';

    try {
      // 如果已经是字符串，直接返回
      if (typeof arrayValue === 'string') {
        console.log(`数组格式化: "${arrayValue}" -> "${arrayValue}" (已是字符串)`);
        return arrayValue;
      }

      // 如果是数组，转换为逗号分隔的字符串
      if (Array.isArray(arrayValue)) {
        const formatted = arrayValue.join(',');
        console.log(`数组格式化: [${arrayValue.join(', ')}] -> "${formatted}"`);
        return formatted;
      }

      // 如果是对象，尝试转换为字符串
      if (typeof arrayValue === 'object') {
        const formatted = JSON.stringify(arrayValue);
        console.log(`对象格式化: ${JSON.stringify(arrayValue)} -> "${formatted}"`);
        return formatted;
      }

      // 其他类型，转换为字符串
      const formatted = String(arrayValue);
      console.log(`其他类型格式化: ${arrayValue} -> "${formatted}"`);
      return formatted;
    } catch (error) {
      console.error(`数组格式化失败: ${arrayValue}`, error);
      return '';
    }
  }

  /**
   * 验证映射后的数据
   * @param {Object} mappedData 映射后的数据
   * @param {string} dataType 数据类型（customer/agreement）
   * @returns {Object} 验证结果
   */
  validateMappedData(mappedData, dataType) {
    const errors = [];
    const warnings = [];

    if (dataType === 'customer') {
      // 客户数据验证
      if (!mappedData.customerName) {
        errors.push('客户名称不能为空');
      }
      if (!mappedData.externalCustomerId) {
        errors.push('外部客户ID不能为空');
      }
    } else if (dataType === 'agreement') {
      // 协议数据验证
      if (!mappedData.title) {
        warnings.push('协议标题为空');
      }
      if (!mappedData.customerName) {
        errors.push('关联客户名称不能为空');
      }
      if (!mappedData.externalAgreementId) {
        errors.push('外部协议ID不能为空');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 提取关键字段用于日志记录
   * @param {Object} data 数据对象
   * @param {string} type 数据类型
   * @returns {Object} 关键字段
   */
  extractKeyFields(data, type) {
    if (type === 'customer') {
      return {
        customerName: data.customerName,
        externalCustomerId: data.externalCustomerId,
        seedingPlatform: data.seedingPlatform
      };
    } else if (type === 'agreement') {
      return {
        title: data.title,
        customerName: data.customerName,
        externalAgreementId: data.externalAgreementId,
        cooperationAmount: data.cooperationAmount
      };
    }
    return {};
  }
}

module.exports = CrmDataMappingService;
