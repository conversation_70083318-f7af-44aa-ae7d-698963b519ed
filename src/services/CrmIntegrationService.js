/**
 * CRM集成服务
 *
 * 功能说明：
 * - 与钉钉CRM系统进行数据同步
 * - 实现客户数据和协议数据的拉取
 * - 提供token缓存和频率限制机制
 * - 支持数据映射和本地存储
 *
 * 主要功能：
 * - CRM鉴权和token管理
 * - 客户数据列表获取
 * - 协议数据查询
 * - 数据同步到本地数据库
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');
const config = require('../config/env');
const { CooperationManagement } = require('../models');
const CrmDataMappingService = require('./CrmDataMappingService');
const moment = require('moment');

class CrmIntegrationService {
  constructor() {
    // CRM配置
    this.config = config.crm;

    // 创建axios实例（使用代理）
    this.httpClient = axios.create({
      baseURL: this.config.proxyUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    // Token缓存
    this.tokenCache = {
      corpAccessToken: null,
      expiresAt: null
    };

    // 频率限制队列
    this.requestQueue = [];
    this.isProcessingQueue = false;

    // 初始化数据映射服务
    this.dataMappingService = new CrmDataMappingService();

    // 初始化请求拦截器
    this.setupInterceptors();
  }

  /**
   * 设置请求拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.httpClient.interceptors.request.use(
      config => {
        console.log(`� CRM API请求: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      error => {
        console.error('❌ CRM API请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      response => {
        console.log(`✅ CRM API响应成功: ${response.status}`);
        return response;
      },
      error => {
        console.error('❌ CRM API响应错误:', error.response?.status, error.response?.data);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 获取corpAccessToken
   * @returns {Promise<string>} corpAccessToken
   */
  async getCorpAccessToken() {
    try {
      // 检查缓存的token是否有效
      if (this.isTokenValid()) {
        console.log('� 使用缓存的corpAccessToken');
        return this.tokenCache.corpAccessToken;
      }

      console.log('🔄 获取新的corpAccessToken...');

      // 请求新的token（使用POST方法）
      const response = await this.httpClient.post('/oapi/corp/corp_access_token/get.json', {
        corpId: this.config.corpId,
        appId: this.config.appId,
        appSecret: this.config.appSecret
      });

      const data = response.data;

      console.log('🔍 CRM API响应数据:', JSON.stringify(data, null, 2));

      if (!data.success) {
        throw new Error(`获取corpAccessToken失败: ${data.message} (错误码: ${data.result})`);
      }

      // 缓存token
      this.tokenCache.corpAccessToken = data.data.corpAccessToken;
      this.tokenCache.expiresAt =
        Date.now() + (this.config.tokenCache.ttl - this.config.tokenCache.refreshBuffer) * 1000;

      console.log('✅ 成功获取corpAccessToken');
      return data.data.corpAccessToken;
    } catch (error) {
      console.error('❌ 获取corpAccessToken失败:', error.message);
      throw new Error(`CRM鉴权失败: ${error.message}`);
    }
  }

  /**
   * 检查token是否有效
   * @returns {boolean}
   */
  isTokenValid() {
    return this.tokenCache.corpAccessToken && this.tokenCache.expiresAt && Date.now() < this.tokenCache.expiresAt;
  }

  /**
   * 获取客户数据列表
   * @param {number} page 页码，从1开始
   * @param {number} limit 每页数量，默认20
   * @returns {Promise<Object>} 客户数据列表
   */
  async getCustomerList(page = 1, limit = 20) {
    try {
      const token = await this.getCorpAccessToken();

      console.log(`📋 获取客户数据列表 - 页码: ${page}, 每页: ${limit}`);

      // 构建请求数据
      const requestData = {
        corpAccessToken: token,
        corpId: this.config.corpId,
        pageSize: limit,
        page: page,
        userId: '0141435327853352', // 固定用户ID
        conditionConfig: [],
        subConditionConfig: [],
        templateId: this.getTemplateId('customer'), // 客户模板ID
        sceneId: 'customer_list_departmental',
        deployId: 'customer'
      };

      const response = await this.makeRateLimitedRequest('/oapi/corp/v1/data/list.json', requestData);

      const data = response.data;

      console.log('🔍 客户列表API响应数据:', data.data?.list?.length || 0);

      if (!data.success) {
        throw new Error(`获取客户列表失败: ${data.message} (错误码: ${data.result})`);
      }

      console.log(`✅ 成功获取客户数据 - 总数: ${data.data?.count || 0}, 当前页: ${data.data?.list?.length || 0}`);

      return {
        total: data.data?.total || 0,
        page: page,
        limit: limit,
        data: data.data?.list || []
      };
    } catch (error) {
      console.error('❌ 获取客户列表失败:', error.message);
      throw new Error(`获取客户数据失败: ${error.message}`);
    }
  }

  /**
   * 根据客户名称获取协议列表
   * @param {string} customerName 客户名称
   * @returns {Promise<Array>} 协议数据列表
   */
  async getAgreementsByCustomer(customerName) {
    try {
      const token = await this.getCorpAccessToken();

      console.log(`📄 获取客户协议数据 - 客户: ${customerName}`);

      // 构建协议查询请求数据
      const requestData = {
        corpAccessToken: token,
        corpId: this.config.corpId,
        deployId: 'contract',
        userId: '013357234921381449', // 固定用户ID
        conditionConfig: [
          {
            conditions: [
              {
                comparison: 'YES',
                deployId: 'contract',
                fieldName: 'custom_name',
                fieldTitle: '客户',
                fieldValue: customerName,
                domType: 'REFER_OBJECT',
                referRule: 'customer',
                fieldType: 0,
                multiValue: 0
              }
            ]
          }
        ],
        page: 1,
        pageSize: 200
      };

      const response = await this.makeRateLimitedRequest('/oapi/corp/v1/data/list.json', requestData);

      const data = response.data;

      console.log('🔍 协议列表API响应数据:', data.data?.list?.length || 0);

      if (!data.success) {
        throw new Error(`获取协议列表失败: ${data.message} (错误码: ${data.result})`);
      }

      const agreements = data.data?.list || [];
      console.log(`✅ 成功获取协议数据 - 客户: ${customerName}, 协议数量: ${agreements.length}`);

      return agreements;
    } catch (error) {
      console.error(`❌ 获取客户协议失败 - 客户: ${customerName}, 错误:`, error.message);
      throw new Error(`获取协议数据失败: ${error.message}`);
    }
  }

  /**
   * 频率限制的请求方法
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   * @returns {Promise<Object>} 响应数据
   */
  async makeRateLimitedRequest(url, data = {}) {
    return new Promise((resolve, reject) => {
      // 添加到请求队列
      this.requestQueue.push({
        url,
        data,
        resolve,
        reject,
        timestamp: Date.now()
      });

      // 处理队列
      this.processRequestQueue();
    });
  }

  /**
   * 处理请求队列（频率限制）
   */
  async processRequestQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.requestQueue.length > 0) {
        const request = this.requestQueue.shift();

        try {
          // 添加请求延迟
          await this.delay(this.config.rateLimit.requestDelay);

          // 发起POST请求
          const response = await this.httpClient.post(request.url, request.data);
          request.resolve(response);
        } catch (error) {
          request.reject(error);
        }
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 根据姓名从CRM系统拉取用户信息
   * @param {string} userName 用户姓名
   * @param {number} page 页码，默认1
   * @param {number} pageSize 每页数量，默认20
   * @returns {Promise<Object>} 用户信息列表
   */
  async getUserByName(userName, page = 1, pageSize = 20) {
    try {
      const token = await this.getCorpAccessToken();

      console.log(`👤 根据姓名查询CRM用户 - 姓名: ${userName}, 页码: ${page}, 每页: ${pageSize}`);

      // 构建请求数据
      const requestData = {
        corpAccessToken: token,
        corpId: this.config.corpId,
        userName: userName,
        page: page,
        pageSize: pageSize
      };

      console.log('📤 CRM用户查询请求数据:', JSON.stringify(requestData, null, 2));

      // 发送请求
      const response = await this.makeRateLimitedRequest('/oapi/corp/v1/user/list.json', requestData);

      console.log('📥 CRM用户查询响应:', JSON.stringify(response.data, null, 2));

      if (response.data.success && response.data.data) {
        console.log(`✅ 查询到 ${response.data.data.length} 个用户`);

        // 格式化用户数据
        const formattedUsers = response.data.data.map(user => ({
          dataId: user.dataId,
          department: user.department,
          departmentName: user.departmentName,
          userId: user.userId,
          userName: user.userName,
          userPicUrl: user.userPicUrl
        }));

        return {
          success: true,
          data: formattedUsers,
          total: response.data.data.length,
          page: page,
          pageSize: pageSize
        };
      } else {
        console.log('❌ CRM用户查询失败:', response.data.message || '未知错误');
        return {
          success: false,
          message: response.data.message || 'CRM用户查询失败',
          data: []
        };
      }
    } catch (error) {
      console.error('❌ CRM用户查询异常:', error.message);
      return {
        success: false,
        message: `CRM用户查询异常: ${error.message}`,
        data: []
      };
    }
  }

  /**
   * 测试CRM连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      console.log('🔍 测试CRM连接...');

      // 测试获取token
      const token = await this.getCorpAccessToken();

      // 测试获取第一页客户数据
      const customerData = await this.getCustomerList(1, 1);

      return {
        success: true,
        message: 'CRM连接测试成功',
        data: {
          tokenValid: !!token,
          customerCount: customerData.total
        }
      };
    } catch (error) {
      console.error('❌ CRM连接测试失败:', error.message);
      return {
        success: false,
        message: `CRM连接测试失败: ${error.message}`,
        data: null
      };
    }
  }

  /**
   * 获取模板ID
   * @param {string} deployId 部署ID
   * @returns {string} 模板ID
   */
  getTemplateId(deployId) {
    const templateIds = {
      customer: 'e5990bd5d09b45b896123dacf3ce2836', // 客户模板ID
      contract: '6c9ead1dbaaf4bfe90bcf111cfd5c453' // 协议模板ID
    };

    return templateIds[deployId];
  }

  /**
   * 创建CRM数据（新增接口）
   * @param {string} deployId 部署ID（customer/contract）
   * @param {Object} data 要创建的数据
   * @returns {Promise<Object>} 创建结果
   */
  async createCrmData(deployId, data) {
    try {
      console.log(`🔄 创建CRM数据 - 类型: ${deployId}`);

      const token = await this.getCorpAccessToken();
      const url = `${config.crm.proxyUrl}/oapi/corp/v1/data/insert.json`;

      //  协议额外补充字段
      if (deployId === 'contract') {
        Object.assign(data, {
          contract_start_date: moment().format('YYYY/MM/DD')
        });
      }

      const requestData = {
        corpAccessToken: token,
        corpId: config.crm.corpId,
        userId: '0141435327853352',
        deployId: deployId,
        templateId: this.getTemplateId(deployId),
        data: data
      };

      console.log(`📤 发送创建请求:`, JSON.stringify(requestData, null, 2));

      const response = await axios.post(url, requestData, {
        timeout: config.crm.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        console.log(`✅ CRM数据创建成功 - ID: ${response.data.data.id}`);
        return {
          success: true,
          dataId: response.data.data.id,
          message: `${deployId}数据创建成功`,
          data: response.data.data
        };
      } else {
        throw new Error(response.data.message || 'CRM数据创建失败');
      }
    } catch (error) {
      console.error(`❌ CRM数据创建失败 (${deployId}):`, error.message);
      throw new Error(`CRM数据创建失败: ${error.message}`);
    }
  }

  /**
   * 更新CRM数据（更新接口）
   * @param {string} deployId 部署ID（customer/contract）
   * @param {number} dataId 数据ID
   * @param {Object} data 要更新的数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateCrmData(deployId, dataId, data) {
    try {
      console.log(`🔄 更新CRM数据 - 类型: ${deployId}, ID: ${dataId}`);

      const token = await this.getCorpAccessToken();
      const url = `${config.crm.proxyUrl}/oapi/corp/v1/data/update.json`;

      const requestData = {
        corpAccessToken: token,
        corpId: config.crm.corpId,
        userId: '0141435327853352',
        deployId: deployId,
        dataId: dataId,
        data: data
      };
      if (this.getTemplateId(deployId)) {
        requestData.templateId = this.getTemplateId(deployId);
      }

      console.log(`📤 发送更新请求:`, JSON.stringify(requestData, null, 2));

      const response = await axios.post(url, requestData, {
        timeout: config.crm.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        console.log(`✅ CRM数据更新成功 - ID: ${dataId}`);
        return {
          success: true,
          dataId: dataId,
          message: `${deployId}数据更新成功`,
          data: response.data.data
        };
      } else {
        throw new Error(response.data.message || 'CRM数据更新失败');
      }
    } catch (error) {
      console.error(`❌ CRM数据更新失败 (${deployId}, ID: ${dataId}):`, error.message);
      throw new Error(`CRM数据更新失败: ${error.message}`);
    }
  }

  /**
   * 同步客户数据到本地数据库
   * @param {Array} customerList 客户数据列表
   * @returns {Promise<Object>} 同步结果
   */
  async syncCustomerData(customerList) {
    const result = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };

    try {
      console.log(`🔄 开始同步客户数据 - 总数: ${customerList.length}`);

      for (const crmCustomer of customerList) {
        try {
          // 映射数据
          const mappedData = this.dataMappingService.mapCustomerData(crmCustomer);

          // 验证数据
          const validation = this.dataMappingService.validateMappedData(mappedData, 'customer');
          if (!validation.isValid) {
            result.failed++;
            result.errors.push({
              customerName: mappedData.customerName,
              errors: validation.errors
            });
            continue;
          }

          // 检查是否已存在
          const existingRecord = await CooperationManagement.findOne({
            where: { externalCustomerId: mappedData.externalCustomerId }
          });

          if (existingRecord) {
            // 更新现有记录
            await existingRecord.update(mappedData);
            result.success++;
            console.log(`✅ 更新客户: ${mappedData.customerName}`);
          } else {
            // 创建新记录
            await CooperationManagement.create(mappedData);
            result.success++;
            console.log(`✅ 创建客户: ${mappedData.customerName}`);
          }
        } catch (error) {
          result.failed++;
          result.errors.push({
            customerName: crmCustomer.custom_name || 'Unknown',
            error: error.message
          });
          console.error(`❌ 同步客户失败: ${crmCustomer.custom_name}`, error.message);
        }
      }

      console.log(`✅ 客户数据同步完成 - 成功: ${result.success}, 失败: ${result.failed}, 跳过: ${result.skipped}`);
      return result;
    } catch (error) {
      console.error('❌ 客户数据同步过程异常:', error.message);
      throw new Error(`客户数据同步失败: ${error.message}`);
    }
  }

  /**
   * 同步协议数据到本地数据库
   * @param {Array} agreementList 协议数据列表
   * @param {string} customerName 关联的客户名称
   * @returns {Promise<Object>} 同步结果
   */
  async syncAgreementData(agreementList, customerName) {
    const result = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };

    try {
      console.log(`🔄 开始同步协议数据 - 客户: ${customerName}, 协议数量: ${agreementList.length}`);

      for (const crmAgreement of agreementList) {
        try {
          // 映射数据
          const mappedData = this.dataMappingService.mapAgreementData(crmAgreement, customerName);

          // 验证数据
          const validation = this.dataMappingService.validateMappedData(mappedData, 'agreement');
          if (!validation.isValid) {
            result.failed++;
            result.errors.push({
              agreementTitle: mappedData.title,
              errors: validation.errors
            });
            continue;
          }

          // 检查是否已存在
          const existingRecord = await CooperationManagement.findOne({
            where: { externalAgreementId: mappedData.externalAgreementId }
          });

          if (existingRecord) {
            // 更新现有记录
            await existingRecord.update(mappedData);
            result.success++;
            console.log(`✅ 更新协议: ${mappedData.title}`);
          } else {
            // 创建新记录
            await CooperationManagement.create(mappedData);
            result.success++;
            console.log(`✅ 创建协议: ${mappedData.title}`);
          }
        } catch (error) {
          result.failed++;
          result.errors.push({
            agreementTitle: crmAgreement.contract_title || 'Unknown',
            error: error.message
          });
          console.error(`❌ 同步协议失败: ${crmAgreement.contract_title}`, error.message);
        }
      }

      console.log(`✅ 协议数据同步完成 - 成功: ${result.success}, 失败: ${result.failed}, 跳过: ${result.skipped}`);
      return result;
    } catch (error) {
      console.error('❌ 协议数据同步过程异常:', error.message);
      throw new Error(`协议数据同步失败: ${error.message}`);
    }
  }

  /**
   * 完整的客户+协议数据同步
   * @param {string} customerName 可选的客户名称过滤
   * @param {number} page 页码
   * @param {number} limit 每页数量
   * @returns {Promise<Object>} 同步结果
   */
  async syncCustomerWithAgreements(customerName = null, page = 1, limit = 20) {
    const syncResult = {
      customerSync: { success: 0, failed: 0, errors: [] },
      agreementSync: { success: 0, failed: 0, errors: [] },
      totalProcessed: 0
    };

    try {
      console.log(
        `🔄 开始完整数据同步 - 页码: ${page}, 每页: ${limit}${customerName ? `, 客户: ${customerName}` : ''}`
      );

      // 1. 获取客户列表
      const customerData = await this.getCustomerList(page, limit, customerName);
      syncResult.totalProcessed = customerData.data.length;

      if (customerData.data.length === 0) {
        console.log('📋 没有找到客户数据');
        return syncResult;
      }

      // 2. 为每个客户整合客户信息和协议信息
      for (const customer of customerData.data) {
        try {
          console.log(`🔄 处理客户: ${customer.custom_name}`);

          // 2.1 映射客户数据
          const mappedCustomerData = this.dataMappingService.mapCustomerData(customer);

          // 2.2 获取该客户的协议数据
          const agreements = await this.getAgreementsByCustomer(customer.custom_name);

          if (agreements.length > 0) {
            // 2.3 为每个协议创建整合记录（客户信息 + 协议信息）
            for (const agreement of agreements) {
              try {
                // 映射协议数据
                const mappedAgreementData = this.dataMappingService.mapAgreementData(agreement, customer.custom_name);

                // 整合客户信息和协议信息
                const integratedData = this.integrateCustomerAndAgreementData(mappedCustomerData, mappedAgreementData);

                // 验证整合后的数据
                const validation = this.dataMappingService.validateMappedData(integratedData, 'agreement');
                if (!validation.isValid) {
                  syncResult.agreementSync.failed++;
                  syncResult.agreementSync.errors.push({
                    customerName: customer.custom_name,
                    agreementTitle: integratedData.title,
                    errors: validation.errors
                  });
                  continue;
                }

                // 检查是否已存在（优先使用协议ID，其次使用客户ID）
                let existingRecord = null;
                if (integratedData.externalAgreementId) {
                  existingRecord = await CooperationManagement.findOne({
                    where: { externalAgreementId: integratedData.externalAgreementId }
                  });
                }

                if (!existingRecord && integratedData.externalCustomerId) {
                  // 如果没有找到协议记录，尝试查找客户记录进行更新
                  existingRecord = await CooperationManagement.findOne({
                    where: {
                      externalCustomerId: integratedData.externalCustomerId,
                      customerName: customer.custom_name
                    }
                  });
                }

                if (existingRecord) {
                  // 更新现有记录
                  await existingRecord.update(integratedData);
                  console.log(`✅ 更新整合记录: ${integratedData.title}`);
                } else {
                  // 创建新记录
                  await CooperationManagement.create(integratedData);
                  console.log(`✅ 创建整合记录: ${integratedData.title}`);
                }

                syncResult.agreementSync.success++;
              } catch (error) {
                console.error(`❌ 同步协议失败: ${agreement.contract_title}`, error.message);
                syncResult.agreementSync.failed++;
                syncResult.agreementSync.errors.push({
                  customerName: customer.custom_name,
                  agreementTitle: agreement.contract_title,
                  error: error.message
                });
              }
            }
          } else {
            // 2.4 如果没有协议数据，只创建客户记录
            try {
              // 检查是否已存在客户记录
              const existingCustomerRecord = await CooperationManagement.findOne({
                where: { externalCustomerId: mappedCustomerData.externalCustomerId }
              });

              if (existingCustomerRecord) {
                // 更新客户信息
                await existingCustomerRecord.update(mappedCustomerData);
                console.log(`✅ 更新客户记录: ${mappedCustomerData.customerName}`);
              } else {
                // 创建新的客户记录
                await CooperationManagement.create(mappedCustomerData);
                console.log(`✅ 创建客户记录: ${mappedCustomerData.customerName}`);
              }

              syncResult.customerSync.success++;
            } catch (error) {
              console.error(`❌ 同步客户失败: ${customer.custom_name}`, error.message);
              syncResult.customerSync.failed++;
              syncResult.customerSync.errors.push({
                customerName: customer.custom_name,
                error: error.message
              });
            }
          }
        } catch (error) {
          console.error(`❌ 处理客户失败: ${customer.custom_name}`, error.message);
          syncResult.customerSync.failed++;
          syncResult.customerSync.errors.push({
            customerName: customer.custom_name,
            error: error.message
          });
        }
      }

      console.log('✅ 完整数据同步完成');
      return syncResult;
    } catch (error) {
      console.error('❌ 完整数据同步失败:', error.message);
      throw new Error(`数据同步失败: ${error.message}`);
    }
  }

  /**
   * 整合客户信息和协议信息
   * @param {Object} customerData 映射后的客户数据
   * @param {Object} agreementData 映射后的协议数据
   * @returns {Object} 整合后的数据
   */
  integrateCustomerAndAgreementData(customerData, agreementData) {
    // 以协议数据为基础，补充客户信息
    const integratedData = { ...agreementData };

    // 补充客户信息字段（如果协议数据中没有的话）
    const customerFields = [
      'customerHomepage', // 客户主页链接
      'customerPublicSea', // 所属公海
      'seedingPlatform', // 种草平台
      'bloggerFansCount', // 博主粉丝量级
      'influencerPlatformId', // 达人平台ID
      'bloggerWechatAndNotes', // 博主微信及备注
      'starMapId' // 星图ID
    ];

    customerFields.forEach(field => {
      if (customerData[field] && !integratedData[field]) {
        integratedData[field] = customerData[field];
      }
    });

    // 确保客户关联信息正确
    integratedData.externalCustomerId = customerData.externalCustomerId;
    integratedData.customerName = customerData.customerName;

    // 设置CRM关联状态为完全关联（既有客户又有协议）
    integratedData.crmLinkStatus = 'fully_linked';

    // 如果协议数据中没有博主名称，使用客户名称
    if (!integratedData.bloggerName && customerData.customerName) {
      integratedData.bloggerName = customerData.customerName;
    }

    // 如果协议数据中没有达人主页链接，使用客户主页链接
    if (!integratedData.influencerHomepage && customerData.customerHomepage) {
      integratedData.influencerHomepage = customerData.customerHomepage;
    }

    console.log(`🔗 整合数据完成: ${integratedData.title} (客户: ${integratedData.customerName})`);
    return integratedData;
  }

  /**
   * 智能同步合作对接数据到CRM
   * @param {Object} cooperationData 合作对接数据
   * @param {Object} options 同步选项
   * @returns {Promise<Object>} 同步结果
   */
  async smartSyncCooperationData(cooperationData, options = {}) {
    try {
      console.log(`🔄 开始智能同步合作对接数据: ${cooperationData.customerName || cooperationData.id}`);

      const syncResult = {
        customerId: null,
        agreementId: null,
        customerSynced: false,
        agreementSynced: false,
        errors: [],
        warnings: []
      };

      // 1. 检测需要同步的数据类型
      const syncNeeds = this.detectSyncNeeds(cooperationData, options);
      console.log(`🔍 检测到同步需求:`, syncNeeds);

      // 2. 同步客户数据
      if (syncNeeds.needCustomerSync) {
        try {
          console.log(`🔄 客户数据同步模式: ${syncNeeds.customerSyncType}`);
          const customerResult = await this.syncCustomerData(cooperationData);
          syncResult.customerId = customerResult.dataId;
          syncResult.customerSynced = true;
          cooperationData.externalCustomerId = customerResult.dataId;
          cooperationData.customer_id = customerResult.dataId;
          console.log(`✅ 客户数据同步成功 - ID: ${customerResult.dataId}`);
        } catch (error) {
          syncResult.errors.push(`客户同步失败: ${error.message}`);
          console.error(`❌ 客户数据同步失败:`, error.message);
        }
      }

      // 3. 同步协议数据
      if (syncNeeds.needAgreementSync) {
        try {
          console.log(`🔄 协议数据同步模式: ${syncNeeds.agreementSyncType}`);
          const agreementResult = await this.syncAgreementData(cooperationData);
          syncResult.agreementId = agreementResult.dataId;
          syncResult.agreementSynced = true;
          cooperationData.externalAgreementId = agreementResult.dataId;
          console.log(`✅ 协议数据同步成功 - ID: ${agreementResult.dataId}`);
        } catch (error) {
          syncResult.errors.push(`协议同步失败: ${error.message}`);
          console.error(`❌ 协议数据同步失败:`, error.message);
        }
      }

      // 4. 更新本地CRM关联状态
      if ((syncResult.customerId || syncResult.agreementId) && cooperationData.id) {
        await this.updateLocalCrmStatus(cooperationData.id, {
          customerId: syncResult.customerId || cooperationData.externalCustomerId,
          agreementId: syncResult.agreementId || cooperationData.externalAgreementId
        });
      } else if (!cooperationData.id && (syncResult.customerId || syncResult.agreementId)) {
        console.warn('⚠️ 无法更新本地CRM状态: 合作记录ID不存在');
      }

      console.log(`✅ 智能同步完成:`, syncResult);
      return syncResult;
    } catch (error) {
      console.error(`❌ 智能同步失败:`, error.message);
      throw new Error(`智能同步失败: ${error.message}`);
    }
  }

  /**
   * 检测同步需求
   * @param {Object} cooperationData 合作对接数据
   * @param {Object} options 选项
   * @returns {Object} 同步需求
   */
  detectSyncNeeds(cooperationData, options) {
    const needs = {
      needCustomerSync: false,
      needAgreementSync: false,
      customerSyncType: 'create', // 'create' 或 'update'
      agreementSyncType: 'create', // 'create' 或 'update'
      reason: []
    };

    // 强制同步选项
    if (options.forceCustomerSync) {
      needs.needCustomerSync = true;
      needs.customerSyncType = cooperationData.externalCustomerId ? 'update' : 'create';
      needs.reason.push('强制同步客户');
    }

    if (options.forceAgreementSync) {
      needs.needAgreementSync = true;
      needs.agreementSyncType = cooperationData.externalAgreementId ? 'update' : 'create';
      needs.reason.push('强制同步协议');
    }

    // 自动检测同步需求
    if (!options.forceCustomerSync && !options.forceAgreementSync) {
      // 如果没有外部客户ID，需要创建客户
      if (!cooperationData.externalCustomerId) {
        needs.needCustomerSync = true;
        needs.customerSyncType = 'create';
        needs.reason.push('缺少外部客户ID');
      }

      // 如果没有外部协议ID，需要创建协议
      if (!cooperationData.externalAgreementId) {
        needs.needAgreementSync = true;
        needs.agreementSyncType = 'create';
        needs.reason.push('缺少外部协议ID');
      }

      // 检测字段变化（如果提供了变化字段信息）
      if (options.changedFields && Array.isArray(options.changedFields)) {
        const customerFields = this.getCustomerRelatedFields();
        const agreementFields = this.getAgreementRelatedFields();

        const hasCustomerChanges = options.changedFields.some(field => customerFields.includes(field));
        const hasAgreementChanges = options.changedFields.some(field => agreementFields.includes(field));

        if (hasCustomerChanges && cooperationData.externalCustomerId) {
          needs.needCustomerSync = true;
          needs.customerSyncType = 'update';
          needs.reason.push('客户相关字段发生变化');
        }

        if (hasAgreementChanges && cooperationData.externalAgreementId) {
          needs.needAgreementSync = true;
          needs.agreementSyncType = 'update';
          needs.reason.push('协议相关字段发生变化');
        }
      }
    }

    return needs;
  }

  /**
   * 获取客户相关字段列表
   * @returns {Array} 客户相关字段
   */
  getCustomerRelatedFields() {
    return [
      // 'customerName',
      // 'customerHomepage',
      // 'customerPublicSea',
      // 'seedingPlatform',
      // 'bloggerFansCount',
      // 'influencerPlatformId',
      // 'bloggerWechatAndNotes',
      // 'starMapId'
      'externalCustomerId', // 客户ID（主键）
      'customerName', // 客户名称
      'customerPublicSea', // 所属公海
      'customerHomepage', // 主页链接
      'seedingPlatform', // 种草平台
      'bloggerFansCount', // 博主粉丝量级
      'influencerPlatformId', // 平台ID
      'bloggerWechatAndNotes' // 备注信息
      // created: 'createdAt', // 创建时间
      // modified: 'updatedAt' // 更新时间
    ];
  }

  /**
   * 获取协议相关字段列表
   * @returns {Array} 协议相关字段
   */
  getAgreementRelatedFields() {
    return [
      // 'title',
      // 'cooperationForm',
      // 'cooperationBrand',
      // 'cooperationProduct',
      // 'cooperationAmount',
      // 'scheduledPublishTime',
      // 'cooperationNotes',
      // 'publishPlatform',
      // 'actualPublishDate',
      // 'publishLink',
      // 'responsiblePerson'
      'externalAgreementId', // 协议ID（主键）
      'title', // 协议标题
      'customerName', // 关联客户名称
      'externalCustomerId', // 关联客户ID
      'cooperationForm', // 合作形式
      'publishPlatform', // 发布平台
      'cooperationBrand', // 合作品牌
      'cooperationProduct', // 合作产品
      'cooperationNotes', // 备注
      'actualPublishDate', //实际发布日期 actualPublishDate
      'scheduledPublishTime', // 约定发布时间
      'cooperationAmount', // 合作金额
      'influencerCommissionRate', // 达人佣金比例
      'payeeName', // 收款人姓名
      'bankAccount', // 银行账号
      'bankName', // 开户行
      'rebateCompleted', // 返点状态
      'publishLink', // 发布链接
      'dataRegistrationDate', // 数据登记日期
      'viewCount', // 观看量
      'likeCount', // 点赞数
      'collectCount', // 收藏数
      'commentCount', // 评论数
      'brandTopicStatus', // 是否加入品牌话题
      'storeSales', // 店铺销售额
      'selfEvaluation' // 自评
    ];
  }

  /**
   * 智能同步客户数据（支持创建和更新）
   * @param {Object} cooperationData 合作对接数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncCustomerData(cooperationData) {
    try {
      // 使用数据映射服务转换数据格式
      const crmCustomerData = this.dataMappingService.mapCooperationToCustomer(cooperationData);

      let result;
      if (cooperationData.externalCustomerId) {
        // 更新现有客户数据
        console.log(`🔄 更新客户数据: ${cooperationData.customerName} (ID: ${cooperationData.externalCustomerId})`);
        result = await this.updateCrmData('customer', cooperationData.externalCustomerId, crmCustomerData);
        result.dataId = cooperationData.externalCustomerId; // 保持原有ID
      } else {
        // 创建新客户数据
        console.log(`🆕 创建客户数据: ${cooperationData.customerName}`);
        result = await this.createCrmData('customer', crmCustomerData);
        cooperationData.externalCustomerId = result.dataId; // 更新外部客户ID
      }

      console.log(`✅ 客户数据同步成功: ${cooperationData.customerName}`);
      return result;
    } catch (error) {
      console.error(`❌ 客户数据同步失败:`, error.message);
      throw error;
    }
  }

  /**
   * 智能同步协议数据（支持创建和更新）
   * @param {Object} cooperationData 合作对接数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncAgreementData(cooperationData) {
    try {
      // 使用数据映射服务转换数据格式
      const crmAgreementData = this.dataMappingService.mapCooperationToAgreement(cooperationData);

      let result;
      if (cooperationData.externalAgreementId) {
        // 更新现有协议数据
        console.log(
          `🔄 更新协议数据: ${cooperationData.title || cooperationData.customerName} (ID: ${
            cooperationData.externalAgreementId
          })`
        );
        result = await this.updateCrmData('contract', cooperationData.externalAgreementId, crmAgreementData);
        result.dataId = cooperationData.externalAgreementId; // 保持原有ID
      } else {
        // 创建新协议数据
        console.log(`🆕 创建协议数据: ${cooperationData.title || cooperationData.customerName}`);
        result = await this.createCrmData('contract', crmAgreementData);
      }

      console.log(`✅ 协议数据同步成功: ${cooperationData.title || cooperationData.customerName}`);
      return result;
    } catch (error) {
      console.error(`❌ 协议数据同步失败:`, error.message);
      throw error;
    }
  }

  /**
   * 同步客户数据到CRM（创建模式）- 保留向后兼容
   * @param {Object} cooperationData 合作对接数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncCustomerToCreation(cooperationData) {
    return this.syncCustomerData(cooperationData);
  }

  /**
   * 同步协议数据到CRM（创建模式）- 保留向后兼容
   * @param {Object} cooperationData 合作对接数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncAgreementToCreation(cooperationData) {
    return this.syncAgreementData(cooperationData);
  }

  /**
   * 更新本地CRM关联状态
   * @param {number} cooperationId 合作对接ID
   * @param {Object} crmIds CRM ID信息
   * @returns {Promise<void>}
   */
  async updateLocalCrmStatus(cooperationId, crmIds) {
    try {
      // 验证必要参数
      if (!cooperationId) {
        throw new Error('合作记录ID不能为空');
      }

      if (!crmIds || (!crmIds.customerId && !crmIds.agreementId)) {
        console.warn('⚠️ 没有有效的CRM ID信息，跳过状态更新');
        return;
      }

      const updateData = {};

      if (crmIds.customerId) {
        updateData.externalCustomerId = crmIds.customerId;
      }

      if (crmIds.agreementId) {
        updateData.externalAgreementId = crmIds.agreementId;
      }

      // 确定CRM关联状态
      if (crmIds.customerId && crmIds.agreementId) {
        updateData.crmLinkStatus = 'fully_linked';
      } else if (crmIds.customerId) {
        updateData.crmLinkStatus = 'customer_linked';
      } else {
        updateData.crmLinkStatus = 'unlinked';
      }

      await CooperationManagement.update(updateData, {
        where: { id: cooperationId }
      });

      console.log(`✅ 本地CRM状态更新成功 - ID: ${cooperationId}`, updateData);
    } catch (error) {
      console.error(`❌ 本地CRM状态更新失败:`, error.message);
      throw error;
    }
  }
}

module.exports = CrmIntegrationService;
