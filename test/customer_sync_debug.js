/**
 * 客户同步调试测试
 * 专门用于调试"芮芮呀"客户同步失败的问题
 */

const CrmDataMappingService = require('../src/services/CrmDataMappingService');

console.log('🔍 开始客户同步调试测试...\n');

// 创建映射服务实例
const mappingService = new CrmDataMappingService();

// 模拟"芮芮呀"客户的数据（可能的格式）
const testCustomerData = {
  customerName: '芮芮呀',
  seedingPlatform: ['小红书'], // 可能是数组格式
  customerHomepage: 'https://www.xiaohongshu.com/user/profile/ruiruiya',
  influencerPlatformId: 'ruiruiya_123',
  bloggerWechatAndNotes: '微信: ruiruiya',
  externalCustomerId: null, // 新客户，没有外部ID
  cooperationForm: '图文',
  publishPlatform: '小红书',
  cooperationBrand: '测试品牌',
  cooperationProduct: '测试产品'
};

console.log('📋 原始客户数据:');
console.log(JSON.stringify(testCustomerData, null, 2));

// 测试1: mapCooperationToCustomer方法（用于同步到CRM）
console.log('\n🧪 测试1: mapCooperationToCustomer方法');
try {
  const crmCustomerData = mappingService.mapCooperationToCustomer(testCustomerData);
  
  console.log('✅ mapCooperationToCustomer转换成功');
  console.log('转换结果:');
  console.log(JSON.stringify(crmCustomerData, null, 2));
  
  // 重点检查seedingPlatform字段
  console.log('\n🎯 关键字段验证:');
  console.log(`customer_check_box_2 (种草平台): "${crmCustomerData.customer_check_box_2}"`);
  console.log(`数据类型: ${typeof crmCustomerData.customer_check_box_2}`);
  console.log(`是否为字符串: ${typeof crmCustomerData.customer_check_box_2 === 'string' ? '✅ 是' : '❌ 否'}`);
  
} catch (error) {
  console.error('❌ mapCooperationToCustomer转换失败:', error.message);
  console.error(error.stack);
}

// 测试2: mapToCrm方法（直接调用）
console.log('\n🧪 测试2: mapToCrm方法（直接调用）');
try {
  const crmData = mappingService.mapToCrm(testCustomerData, 'customer');
  
  console.log('✅ mapToCrm转换成功');
  console.log('转换结果:');
  console.log(JSON.stringify(crmData, null, 2));
  
  // 重点检查seedingPlatform字段
  console.log('\n🎯 关键字段验证:');
  console.log(`customer_check_box_2 (种草平台): "${crmData.customer_check_box_2}"`);
  console.log(`数据类型: ${typeof crmData.customer_check_box_2}`);
  console.log(`是否为字符串: ${typeof crmData.customer_check_box_2 === 'string' ? '✅ 是' : '❌ 否'}`);
  
} catch (error) {
  console.error('❌ mapToCrm转换失败:', error.message);
  console.error(error.stack);
}

// 测试3: 模拟从CRM获取的客户数据（用于同步到本地）
console.log('\n🧪 测试3: mapCustomerData方法（从CRM到本地）');

const crmCustomerFromApi = {
  id: 'customer_123',
  custom_name: '芮芮呀',
  seas_id: 'fd18e0d6b1164e7080f0fa91dc43b0d8',
  customer_textarea_11: 'https://www.xiaohongshu.com/user/profile/ruiruiya',
  customer_check_box_2: ['小红书'], // 可能CRM返回的是数组格式
  customer_textarea_13: 'ruiruiya_123',
  custom_remark: '微信: ruiruiya',
  created: Date.now(),
  modified: Date.now()
};

try {
  const localCustomerData = mappingService.mapCustomerData(crmCustomerFromApi);
  
  console.log('✅ mapCustomerData转换成功');
  console.log('转换结果:');
  console.log(JSON.stringify(localCustomerData, null, 2));
  
  // 检查seedingPlatform字段
  console.log('\n🎯 关键字段验证:');
  console.log(`seedingPlatform: ${JSON.stringify(localCustomerData.seedingPlatform)}`);
  console.log(`数据类型: ${typeof localCustomerData.seedingPlatform}`);
  
} catch (error) {
  console.error('❌ mapCustomerData转换失败:', error.message);
  console.error(error.stack);
}

// 测试4: 检查字段映射配置
console.log('\n🧪 测试4: 检查字段映射配置');

console.log('客户字段映射配置:');
console.log(JSON.stringify(mappingService.customerFieldMapping, null, 2));

console.log('\n反向映射配置:');
console.log(JSON.stringify(mappingService.localToCustomerMapping, null, 2));

// 测试5: 检查数组字段识别
console.log('\n🧪 测试5: 检查数组字段识别');

const testFields = ['seedingPlatform', 'customerName', 'actualPublishDate', 'cooperationForm'];
testFields.forEach(field => {
  const isArrayField = mappingService.isArrayField(field);
  const isDateField = mappingService.isDateField(field);
  console.log(`${field}: 数组字段=${isArrayField}, 日期字段=${isDateField}`);
});

// 测试6: 模拟完整的同步流程
console.log('\n🧪 测试6: 模拟完整的同步流程');

console.log('步骤1: 准备同步数据');
const syncData = {
  ...testCustomerData,
  id: 123,
  title: '小红书-芮芮呀-测试协议'
};

console.log('步骤2: 转换为CRM格式');
try {
  const crmFormatData = mappingService.mapToCrm(syncData, 'customer');
  
  console.log('✅ 转换成功，准备发送到CRM');
  console.log('CRM数据预览:');
  console.log(`- custom_name: ${crmFormatData.custom_name}`);
  console.log(`- customer_check_box_2: "${crmFormatData.customer_check_box_2}" (${typeof crmFormatData.customer_check_box_2})`);
  console.log(`- customer_textarea_11: ${crmFormatData.customer_textarea_11}`);
  
  // 验证是否会导致CRM错误
  if (typeof crmFormatData.customer_check_box_2 !== 'string') {
    console.log('⚠️ 警告: customer_check_box_2不是字符串，可能导致CRM同步失败');
  } else {
    console.log('✅ customer_check_box_2是字符串，应该能正常同步');
  }
  
} catch (error) {
  console.error('❌ 同步流程测试失败:', error.message);
}

console.log('\n🏁 调试测试完成！');

// 如果是直接运行此脚本
if (require.main === module) {
  console.log('\n💡 调试结果分析：');
  console.log('1. 检查mapCooperationToCustomer方法是否正确处理seedingPlatform数组');
  console.log('2. 检查mapToCrm方法是否正确识别和转换数组字段');
  console.log('3. 检查mapCustomerData方法是否正确处理从CRM返回的数组数据');
  console.log('4. 验证字段映射配置是否正确');
  
  console.log('\n🔧 如果仍然出现错误，可能的原因：');
  console.log('- CRM API返回的数据格式与预期不符');
  console.log('- 其他字段也存在类似的数组/对象格式问题');
  console.log('- 数据在传输过程中被其他中间件修改');
  console.log('- CRM系统对字段格式的要求发生了变化');
}
